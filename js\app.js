/**
 * Main Application Logic for KIPL Warehouse Management System
 * Modern, responsive UI with headless architecture
 */

class KIPLApp {
  constructor() {
    this.currentPage = 'dashboard';
    this.sidebarCollapsed = false;
    this.currentData = {};
    this.init();
  }

  init() {
    try {
      console.log('Initializing KIPL App...');
      this.setupEventListeners();
      this.loadInitialData();
      this.hideLoadingSpinner();
      this.navigateToPage('production-plan'); // Start with production plan
      console.log('KIPL App initialized successfully');
    } catch (error) {
      console.error('Error initializing app:', error);
      document.body.innerHTML = '<div style="padding: 20px; color: red;">Error initializing application. Please check the console for details.</div>';
    }
  }

  setupEventListeners() {
    // Sidebar toggle
    const sidebarToggle = document.getElementById('sidebarToggle');
    if (sidebarToggle) {
      sidebarToggle.addEventListener('click', () => this.toggleSidebar());
    }

    // Navigation items
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
      item.addEventListener('click', (e) => {
        e.preventDefault();
        const page = item.getAttribute('data-page');
        this.navigateToPage(page);
      });
    });

    // User profile dropdown
    const userProfile = document.getElementById('userProfile');
    if (userProfile) {
      userProfile.addEventListener('click', () => this.toggleUserMenu());
    }

    // Modal overlay
    const modalOverlay = document.getElementById('modal-overlay');
    if (modalOverlay) {
      modalOverlay.addEventListener('click', () => {
        const modals = document.querySelectorAll('.modal.show');
        modals.forEach(modal => UIComponents.closeModal(modal));
      });
    }

    // Responsive sidebar
    this.setupResponsiveSidebar();
  }

  setupResponsiveSidebar() {
    const mediaQuery = window.matchMedia('(max-width: 1024px)');
    
    const handleMediaQuery = (e) => {
      const sidebar = document.getElementById('sidebar');
      if (e.matches) {
        // Mobile view
        sidebar.classList.remove('collapsed');
        this.sidebarCollapsed = false;
      }
    };

    mediaQuery.addListener(handleMediaQuery);
    handleMediaQuery(mediaQuery);
  }

  toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    this.sidebarCollapsed = !this.sidebarCollapsed;
    
    if (this.sidebarCollapsed) {
      sidebar.classList.add('collapsed');
    } else {
      sidebar.classList.remove('collapsed');
    }
  }

  toggleUserMenu() {
    // Implement user menu dropdown
    UIComponents.showToast('User menu clicked', 'info');
  }

  navigateToPage(page) {
    // Update active navigation
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
      item.classList.remove('active');
      if (item.getAttribute('data-page') === page) {
        item.classList.add('active');
      }
    });

    // Update page title and breadcrumb
    this.updatePageHeader(page);

    // Load page content
    this.loadPageContent(page);
    this.currentPage = page;
  }

  updatePageHeader(page) {
    const pageTitle = document.getElementById('pageTitle');
    const breadcrumb = document.getElementById('breadcrumb');
    
    const pageConfig = {
      'dashboard': { title: 'Dashboard', breadcrumb: ['Home', 'Dashboard'] },
      'production-plan': { title: 'Production Plan', breadcrumb: ['Home', 'Production Plan'] },
      'firm-order': { title: 'Firm Order', breadcrumb: ['Home', 'Firm Order'] },
      'logistics': { title: 'Logistics', breadcrumb: ['Home', 'Logistics'] },
      'manufacturing': { title: 'Manufacturing', breadcrumb: ['Home', 'Manufacturing'] },
      'warehouse': { title: 'Warehouse Management', breadcrumb: ['Home', 'Warehouse'] },
      'reports': { title: 'Reports', breadcrumb: ['Home', 'Reports'] },
      'settings': { title: 'Settings', breadcrumb: ['Home', 'Settings'] },
      'archive': { title: 'Archive', breadcrumb: ['Home', 'Archive'] }
    };

    const config = pageConfig[page] || { title: 'KIPL WMS', breadcrumb: ['Home'] };
    
    if (pageTitle) {
      pageTitle.textContent = config.title;
    }
    
    if (breadcrumb) {
      breadcrumb.innerHTML = config.breadcrumb.map((item, index) => {
        if (index === config.breadcrumb.length - 1) {
          return `<span>${item}</span>`;
        }
        return `<span>${item}</span><span class="material-icons">chevron_right</span>`;
      }).join('');
    }
  }

  async loadPageContent(page) {
    const contentArea = document.getElementById('contentArea');
    if (!contentArea) return;

    // Show loading
    contentArea.innerHTML = '<div class="loading-content">Loading...</div>';

    try {
      switch (page) {
        case 'dashboard':
          await this.loadDashboard();
          break;
        case 'production-plan':
          await this.loadProductionPlan();
          break;
        case 'firm-order':
          await this.loadFirmOrder();
          break;
        default:
          this.loadComingSoon(page);
      }
    } catch (error) {
      console.error('Error loading page content:', error);
      UIComponents.showToast('Error loading page content', 'error');
    }
  }

  async loadProductionPlan() {
    const contentArea = document.getElementById('contentArea');

    try {
      // Define table columns exactly matching original KIPL design with ALL columns
      const columns = [
        {
          key: 'actions',
          title: '',
          width: 80,
          resizable: true,
          render: (value, row) => this.createActionIcons(row)
        },
        {
          key: 'status',
          title: 'Status',
          width: 120,
          resizable: true,
          render: (value, row) => UIComponents.createStatusBadge(value, row.priority)
        },
        { key: 'revisionNo', title: 'Revision #', width: 80, resizable: true },
        { key: 'market', title: 'Market', width: 100, resizable: true },
        { key: 'prodMonth', title: 'Prod Month', width: 90, resizable: true },
        { key: 'model', title: 'Model', width: 120, resizable: true },
        { key: 'machineNumber', title: 'Machine #', width: 100, resizable: true },
        { key: 'specPattern', title: 'SPEC Pattern', width: 100, resizable: true },
        { key: 'orderNumber', title: 'Order #', width: 100, resizable: true },
        { key: 'cabin', title: 'Cabin', width: 80, resizable: true },
        { key: 'arm', title: 'Arm', width: 100, resizable: true },
        { key: 'bucket', title: 'Bucket', width: 150, resizable: true },
        { key: 'dgms', title: 'DGMS', width: 80, resizable: true },
        { key: 'meshGuard', title: 'Mesh Guard / Add', width: 140, resizable: true },
        { key: 'productionStart', title: 'Production Start Date', width: 150, resizable: true }
      ];

      // Create enhanced data table with UX improvements
      let dataTable;
      try {
        dataTable = this.createEnhancedDataTable(
          window.mockData.productionPlans,
          columns,
          {
            title: 'Production Plan',
            showSearch: true,
            showFilters: true,
            enableQuickEdit: true,
            enableBulkActions: true
          }
        );
      } catch (error) {
        console.error('Error creating enhanced table, falling back to basic table:', error);
        // Fallback to basic table
        dataTable = UIComponents.createDataTable(
          window.mockData.productionPlans,
          columns,
          {
            title: 'Production Plan',
            showAddButton: true,
            pagination: true
          }
        );
      }

      contentArea.innerHTML = '';
      contentArea.appendChild(dataTable);
    } catch (error) {
      console.error('Error loading production plan:', error);
      contentArea.innerHTML = '<div class="error-message">Error loading production plan. Please check the console for details.</div>';
    }
  }

  createActionIcons(row) {
    const container = document.createElement('div');
    container.className = 'action-icons';

    // View icon
    const viewIcon = document.createElement('span');
    viewIcon.className = 'material-icons action-icon';
    viewIcon.textContent = 'visibility';
    viewIcon.title = 'View Production Details';
    viewIcon.setAttribute('data-tooltip', 'View production plan details');
    viewIcon.onclick = (e) => {
      e.stopPropagation();
      this.viewRowDetails(row);
    };

    // Edit icon
    const editIcon = document.createElement('span');
    editIcon.className = 'material-icons action-icon';
    editIcon.textContent = 'edit';
    editIcon.title = 'Edit Production Plan';
    editIcon.setAttribute('data-tooltip', 'Quick edit production details');
    editIcon.onclick = (e) => {
      e.stopPropagation();
      this.quickEditRow(row);
    };

    // Copy icon
    const copyIcon = document.createElement('span');
    copyIcon.className = 'material-icons action-icon';
    copyIcon.textContent = 'content_copy';
    copyIcon.title = 'Copy Production Plan';
    copyIcon.setAttribute('data-tooltip', 'Copy this production plan');
    copyIcon.onclick = (e) => {
      e.stopPropagation();
      this.copyRowData(row);
    };

    // Delete icon
    const deleteIcon = document.createElement('span');
    deleteIcon.className = 'material-icons action-icon';
    deleteIcon.textContent = 'delete';
    deleteIcon.title = 'Delete Production Plan';
    deleteIcon.setAttribute('data-tooltip', 'Delete this production plan');
    deleteIcon.onclick = (e) => {
      e.stopPropagation();
      this.deleteRow(row);
    };

    container.appendChild(viewIcon);
    container.appendChild(editIcon);
    container.appendChild(copyIcon);
    container.appendChild(deleteIcon);

    return container;
  }

  async loadFirmOrder() {
    const contentArea = document.getElementById('contentArea');
    
    // Create a similar table for firm orders
    const columns = [
      { key: 'status', title: 'Status', render: (value, row) => UIComponents.createStatusBadge(value, row.priority) },
      { key: 'market', title: 'Market' },
      { key: 'model', title: 'Model' },
      { key: 'orderNumber', title: 'Order #' },
      { key: 'cabin', title: 'Cabin' },
      { key: 'productionStart', title: 'Start Date' }
    ];

    const dataTable = UIComponents.createDataTable(
      window.mockData.productionPlans.slice(0, 3), // Use subset for firm orders
      columns,
      {
        title: 'Firm Orders',
        showAddButton: true
      }
    );

    contentArea.innerHTML = '';
    contentArea.appendChild(dataTable);
  }

  async loadDashboard() {
    const contentArea = document.getElementById('contentArea');
    
    // Create dashboard layout
    const dashboard = document.createElement('div');
    dashboard.className = 'dashboard';
    
    // KPI Cards
    const kpiContainer = document.createElement('div');
    kpiContainer.className = 'kpi-container';
    
    const kpis = [
      { title: 'Total Orders', value: '156', icon: 'assignment', color: 'primary' },
      { title: 'Completed', value: '89', icon: 'check_circle', color: 'success' },
      { title: 'Pending', value: '45', icon: 'pending', color: 'warning' },
      { title: 'Delayed', value: '22', icon: 'error', color: 'error' }
    ];

    kpis.forEach(kpi => {
      const kpiCard = this.createKPICard(kpi);
      kpiContainer.appendChild(kpiCard);
    });

    dashboard.appendChild(kpiContainer);
    
    // Recent activities
    const recentSection = document.createElement('div');
    recentSection.className = 'dashboard-section';
    recentSection.innerHTML = `
      <h3>Recent Production Activities</h3>
      <div class="activity-list">
        <div class="activity-item">
          <span class="material-icons">check_circle</span>
          <span>Production completed for Order #S86NSS64D</span>
          <span class="activity-time">2 hours ago</span>
        </div>
        <div class="activity-item">
          <span class="material-icons">warning</span>
          <span>Delay reported for Order #E45ABC123</span>
          <span class="activity-time">4 hours ago</span>
        </div>
      </div>
    `;

    dashboard.appendChild(recentSection);
    
    contentArea.innerHTML = '';
    contentArea.appendChild(dashboard);
  }

  createKPICard(kpi) {
    const card = document.createElement('div');
    card.className = `kpi-card kpi-${kpi.color}`;
    
    card.innerHTML = `
      <div class="kpi-icon">
        <span class="material-icons">${kpi.icon}</span>
      </div>
      <div class="kpi-content">
        <div class="kpi-value">${kpi.value}</div>
        <div class="kpi-title">${kpi.title}</div>
      </div>
    `;
    
    return card;
  }

  // Enhanced Data Table with UX improvements
  createEnhancedDataTable(data, columns, options = {}) {
    try {
      const container = document.createElement('div');
      container.className = 'modern-data-table-container';

      // Enhanced header with quick actions
      const header = document.createElement('div');
      header.className = 'table-header';

      const leftSection = document.createElement('div');
      leftSection.style.display = 'flex';
      leftSection.style.alignItems = 'center';
      leftSection.style.gap = '12px';

      const title = document.createElement('span');
      title.className = 'table-title';
      title.textContent = options.title || 'Data Table';
      leftSection.appendChild(title);

      // Quick filter buttons for status
      if (options.showFilters) {
        const filterBtns = ['All', 'Kitting Completed', 'In Progress', 'Pending', 'Quality Check', 'On Hold', 'Cancelled'];
        filterBtns.forEach(filter => {
          const btn = document.createElement('button');
          btn.className = 'filter-btn';
          btn.textContent = filter;
          btn.onclick = () => this.filterTable(filter);
          leftSection.appendChild(btn);
        });
      }

      const rightSection = document.createElement('div');
      rightSection.style.display = 'flex';
      rightSection.style.alignItems = 'center';
      rightSection.style.gap = '8px';

      // Global search
      if (options.showSearch) {
        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.placeholder = 'Search all columns...';
        searchInput.className = 'quick-search';
        searchInput.oninput = (e) => this.quickSearch(e.target.value);
        rightSection.appendChild(searchInput);
      }

      // Add New button
      const addBtn = document.createElement('button');
      addBtn.className = 'add-new-btn';
      addBtn.innerHTML = '<span class="material-icons">add</span> Add New';
      addBtn.onclick = () => this.addNewRecord();
      rightSection.appendChild(addBtn);

      header.appendChild(leftSection);
      header.appendChild(rightSection);
      container.appendChild(header);

      // Column-specific search filters
      const columnFilters = this.createColumnFilters(columns);
      container.appendChild(columnFilters);

      // Enhanced table with original KIPL styling
      const tableData = this.paginateData(data, 1, 10); // Start with page 1, 10 items per page
      const table = UIComponents.createDataTable(tableData.data, columns, { showHeader: false });
      container.appendChild(table);

      // Add pagination
      const pagination = this.createPagination(tableData.currentPage, tableData.totalPages, data);
      container.appendChild(pagination);

      // Store original data for filtering/searching
      container.originalData = data;
      container.filteredData = data;
      container.currentPage = 1;
      container.itemsPerPage = 10;

      return container;
    } catch (error) {
      console.error('Error in createEnhancedDataTable:', error);
      // Return basic fallback
      const container = document.createElement('div');
      container.className = 'modern-data-table-container';
      container.innerHTML = `
        <div class="table-header">
          <h2 class="table-title">${options.title || 'Data Table'}</h2>
        </div>
        <div style="padding: 20px;">Loading table data...</div>
      `;
      return container;
    }
  }

  // Add modern Excel-like features - Simplified
  addModernTableFeatures(container) {
    try {
      const table = container.querySelector('.modern-data-table');
      if (!table) {
        console.log('No table found for modern features');
        return;
      }

      console.log('Adding modern table features...');
      // For now, just add basic row selection
      this.addBasicRowSelection(table);
    } catch (error) {
      console.error('Error adding modern features:', error);
    }
  }

  // Basic row selection without complex features
  addBasicRowSelection(table) {
    try {
      const rows = table.querySelectorAll('tbody tr');
      rows.forEach(row => {
        row.addEventListener('click', () => {
          // Remove previous selection
          table.querySelectorAll('tbody tr.selected').forEach(r => r.classList.remove('selected'));
          // Add selection to clicked row
          row.classList.add('selected');
        });
      });
    } catch (error) {
      console.error('Error in basic row selection:', error);
    }
  }

  // Commented out complex row selection for now
  /*
  addRowSelection(table) {
    // Complex selection logic commented out to avoid loading issues
  }
  */

  // Commented out context menu for now
  /*
  addContextMenu(table) {
    // Complex context menu logic commented out
  }
  */

  addKeyboardShortcuts(table) {
    // Show keyboard shortcuts hint
    const shortcutsHint = document.createElement('div');
    shortcutsHint.className = 'keyboard-shortcuts';
    shortcutsHint.innerHTML = `
      <div class="shortcut"><span class="key">Ctrl+A</span> Select All</div>
      <div class="shortcut"><span class="key">Ctrl+C</span> Copy</div>
      <div class="shortcut"><span class="key">Del</span> Delete</div>
      <div class="shortcut"><span class="key">F2</span> Edit</div>
      <div class="shortcut"><span class="key">Esc</span> Clear Selection</div>
    `;
    document.body.appendChild(shortcutsHint);

    // Show/hide shortcuts on Alt key
    document.addEventListener('keydown', (e) => {
      if (e.altKey) {
        shortcutsHint.classList.add('show');
      }

      // Handle keyboard shortcuts
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 'a':
            e.preventDefault();
            this.selectAllRows(table);
            break;
          case 'c':
            e.preventDefault();
            this.copySelectedRows(table);
            break;
        }
      }

      switch (e.key) {
        case 'Delete':
          this.deleteSelectedRows(table);
          break;
        case 'F2':
          this.editSelectedRow(table);
          break;
        case 'Escape':
          this.clearSelection(table);
          break;
      }
    });

    document.addEventListener('keyup', (e) => {
      if (!e.altKey) {
        shortcutsHint.classList.remove('show');
      }
    });
  }

  addColumnSorting(table) {
    const headers = table.querySelectorAll('th.sortable');

    headers.forEach(header => {
      header.addEventListener('click', () => {
        const column = header.getAttribute('data-column');
        this.sortTableByColumn(table, column, header);
      });
    });
  }

  // Helper methods for modern features
  updateSelectionInfo(count) {
    // Update UI to show selection count
    let selectionInfo = document.getElementById('selection-info');

    if (count > 0) {
      if (!selectionInfo) {
        selectionInfo = document.createElement('div');
        selectionInfo.id = 'selection-info';
        selectionInfo.className = 'selection-info';
        document.body.appendChild(selectionInfo);
      }
      selectionInfo.textContent = `${count} row${count > 1 ? 's' : ''} selected`;
      selectionInfo.style.display = 'block';
    } else {
      if (selectionInfo) {
        selectionInfo.style.display = 'none';
      }
    }
  }

  handleContextAction(action, row) {
    const rowData = this.getRowData(row);

    switch (action) {
      case 'view':
        this.viewRowDetails(rowData);
        break;
      case 'edit':
        this.quickEditRow(rowData);
        break;
      case 'copy':
        this.copyRowData(rowData);
        break;
      case 'delete':
        this.deleteRow(rowData);
        break;
    }
  }

  selectAllRows(table) {
    const rows = table.querySelectorAll('tbody tr');
    const selectedRows = table.selectedRows || new Set();

    rows.forEach(row => {
      row.classList.add('selected');
      selectedRows.add(row);
    });

    table.selectedRows = selectedRows;
    this.updateSelectionInfo(rows.length);
  }

  copySelectedRows(table) {
    const selectedRows = table.querySelectorAll('tbody tr.selected');
    const data = Array.from(selectedRows).map(row => this.getRowData(row));

    // Copy to clipboard
    const text = data.map(row => Object.values(row).join('\t')).join('\n');
    navigator.clipboard.writeText(text);
    UIComponents.showToast(`Copied ${data.length} rows to clipboard`, 'success');
  }

  deleteSelectedRows(table) {
    const selectedRows = table.querySelectorAll('tbody tr.selected');
    if (selectedRows.length > 0) {
      if (confirm(`Delete ${selectedRows.length} selected rows?`)) {
        selectedRows.forEach(row => {
          const rowData = this.getRowData(row);
          this.deleteRow(rowData);
        });
      }
    }
  }

  editSelectedRow(table) {
    const selectedRow = table.querySelector('tbody tr.selected');
    if (selectedRow) {
      const rowData = this.getRowData(selectedRow);
      this.quickEditRow(rowData);
    }
  }

  clearSelection(table) {
    const selectedRows = table.querySelectorAll('tbody tr.selected');
    selectedRows.forEach(row => row.classList.remove('selected'));
    this.updateSelectionInfo(0);
  }

  getRowData(row) {
    const id = row.getAttribute('data-row-id');
    return window.mockData.productionPlans.find(p => p.id == id);
  }

  copyRowData(rowData) {
    const text = Object.values(rowData).join('\t');
    navigator.clipboard.writeText(text);
    UIComponents.showToast('Row copied to clipboard', 'success');
  }

  sortTableByColumn(table, column, header) {
    // Toggle sort direction
    const isAsc = header.classList.contains('sort-asc');

    // Clear all sort classes
    table.querySelectorAll('th').forEach(th => {
      th.classList.remove('sort-asc', 'sort-desc');
    });

    // Set new sort direction
    header.classList.add(isAsc ? 'sort-desc' : 'sort-asc');

    // Sort data and refresh table
    const sortedData = [...window.mockData.productionPlans].sort((a, b) => {
      const aVal = a[column] || '';
      const bVal = b[column] || '';

      if (isAsc) {
        return bVal.toString().localeCompare(aVal.toString());
      } else {
        return aVal.toString().localeCompare(bVal.toString());
      }
    });

    // Update table with sorted data
    this.updateTableData(table, sortedData);
    UIComponents.showToast(`Sorted by ${column}`, 'info');
  }

  updateTableData(table, data) {
    const tbody = table.querySelector('tbody');
    tbody.innerHTML = '';

    const columns = [
      { key: 'actions', render: (value, row) => this.createActionIcons(row) },
      { key: 'status', render: (value, row) => UIComponents.createStatusBadge(value, row.priority) },
      { key: 'revisionNo' },
      { key: 'market' },
      { key: 'prodMonth' },
      { key: 'model' },
      { key: 'machineNumber' },
      { key: 'specPattern' },
      { key: 'orderNumber' },
      { key: 'cabin' },
      { key: 'arm' },
      { key: 'bucket' },
      { key: 'dgms' },
      { key: 'meshGuard' },
      { key: 'productionStart' }
    ];

    data.forEach(row => {
      const tr = document.createElement('tr');
      tr.className = 'table-row';
      tr.setAttribute('data-row-id', row.id);

      columns.forEach(column => {
        const td = document.createElement('td');
        td.setAttribute('data-column', column.key);

        if (column.render) {
          const rendered = column.render(row[column.key], row);
          if (typeof rendered === 'string') {
            td.innerHTML = rendered;
          } else {
            td.appendChild(rendered);
          }
        } else {
          td.textContent = row[column.key] || '';
        }

        tr.appendChild(td);
      });

      tbody.appendChild(tr);
    });

    // Re-add modern features to new rows
    this.addRowSelection(table);
  }

  // Create column-specific filters
  createColumnFilters(columns) {
    const filtersContainer = document.createElement('div');
    filtersContainer.className = 'column-filters';

    const filtersTitle = document.createElement('div');
    filtersTitle.className = 'filters-title';
    filtersTitle.innerHTML = '<span class="material-icons">filter_list</span> Column Filters';
    filtersContainer.appendChild(filtersTitle);

    const filtersRow = document.createElement('div');
    filtersRow.className = 'filters-row';

    // Add filters for key columns
    const filterableColumns = [
      { key: 'status', title: 'Status', type: 'select', options: ['All', 'Kitting Completed', 'In Progress', 'Pending', 'Quality Check', 'On Hold', 'Cancelled'] },
      { key: 'market', title: 'Market', type: 'select', options: ['All', 'DOMESTIC', 'EXPORT'] },
      { key: 'model', title: 'Model', type: 'text' },
      { key: 'cabin', title: 'Cabin', type: 'select', options: ['All', 'AC', 'NON-AC'] },
      { key: 'revisionNo', title: 'Revision', type: 'select', options: ['All', '1', '2', '3', '4', '5'] }
    ];

    filterableColumns.forEach(column => {
      const filterGroup = document.createElement('div');
      filterGroup.className = 'filter-group';

      const label = document.createElement('label');
      label.textContent = column.title;
      label.className = 'filter-label';
      filterGroup.appendChild(label);

      if (column.type === 'select') {
        const select = document.createElement('select');
        select.className = 'filter-select';
        select.setAttribute('data-column', column.key);

        column.options.forEach(option => {
          const optionElement = document.createElement('option');
          optionElement.value = option === 'All' ? '' : option;
          optionElement.textContent = option;
          select.appendChild(optionElement);
        });

        select.onchange = (e) => this.applyColumnFilter(column.key, e.target.value);
        filterGroup.appendChild(select);
      } else {
        const input = document.createElement('input');
        input.type = 'text';
        input.className = 'filter-input';
        input.placeholder = `Filter ${column.title}...`;
        input.setAttribute('data-column', column.key);
        input.oninput = (e) => this.applyColumnFilter(column.key, e.target.value);
        filterGroup.appendChild(input);
      }

      filtersRow.appendChild(filterGroup);
    });

    // Clear filters button
    const clearBtn = document.createElement('button');
    clearBtn.className = 'clear-filters-btn';
    clearBtn.innerHTML = '<span class="material-icons">clear</span> Clear';
    clearBtn.onclick = () => this.clearAllFilters();
    filtersRow.appendChild(clearBtn);

    filtersContainer.appendChild(filtersRow);
    return filtersContainer;
  }

  // Pagination methods
  paginateData(data, page, itemsPerPage) {
    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedData = data.slice(startIndex, endIndex);

    return {
      data: paginatedData,
      currentPage: page,
      totalPages: Math.ceil(data.length / itemsPerPage),
      totalItems: data.length,
      itemsPerPage: itemsPerPage
    };
  }

  createPagination(currentPage, totalPages, allData) {
    const paginationContainer = document.createElement('div');
    paginationContainer.className = 'pagination-container';

    const paginationInfo = document.createElement('div');
    paginationInfo.className = 'pagination-info';
    const startItem = ((currentPage - 1) * 10) + 1;
    const endItem = Math.min(currentPage * 10, allData.length);
    paginationInfo.textContent = `Showing ${startItem}-${endItem} of ${allData.length} entries`;

    const paginationControls = document.createElement('div');
    paginationControls.className = 'pagination-controls';

    // Items per page selector
    const itemsPerPageGroup = document.createElement('div');
    itemsPerPageGroup.className = 'items-per-page';
    itemsPerPageGroup.innerHTML = `
      <label>Show:</label>
      <select class="items-per-page-select">
        <option value="10" ${10 === 10 ? 'selected' : ''}>10</option>
        <option value="25" ${10 === 25 ? 'selected' : ''}>25</option>
        <option value="50" ${10 === 50 ? 'selected' : ''}>50</option>
        <option value="100" ${10 === 100 ? 'selected' : ''}>100</option>
      </select>
      <label>entries</label>
    `;

    // Previous button
    const prevBtn = document.createElement('button');
    prevBtn.className = 'pagination-btn';
    prevBtn.innerHTML = '<span class="material-icons">chevron_left</span> Previous';
    prevBtn.disabled = currentPage === 1;
    prevBtn.onclick = () => this.changePage(currentPage - 1);

    // Page numbers
    const pageNumbers = document.createElement('div');
    pageNumbers.className = 'page-numbers';

    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
      const pageBtn = document.createElement('button');
      pageBtn.className = `page-btn ${i === currentPage ? 'active' : ''}`;
      pageBtn.textContent = i;
      pageBtn.onclick = () => this.changePage(i);
      pageNumbers.appendChild(pageBtn);
    }

    // Next button
    const nextBtn = document.createElement('button');
    nextBtn.className = 'pagination-btn';
    nextBtn.innerHTML = 'Next <span class="material-icons">chevron_right</span>';
    nextBtn.disabled = currentPage === totalPages;
    nextBtn.onclick = () => this.changePage(currentPage + 1);

    paginationControls.appendChild(itemsPerPageGroup);
    paginationControls.appendChild(prevBtn);
    paginationControls.appendChild(pageNumbers);
    paginationControls.appendChild(nextBtn);

    paginationContainer.appendChild(paginationInfo);
    paginationContainer.appendChild(paginationControls);

    return paginationContainer;
  }

  changePage(newPage) {
    const container = document.querySelector('.data-table-container');
    const filteredData = container.filteredData || container.originalData;
    const itemsPerPage = container.itemsPerPage || 10;

    const paginatedData = this.paginateData(filteredData, newPage, itemsPerPage);

    // Update table
    this.updateTableWithPagination(container, paginatedData, filteredData);

    container.currentPage = newPage;
  }

  updateTableWithPagination(container, paginatedData, allData) {
    // Update table data
    const table = container.querySelector('.modern-data-table');
    this.updateTableData(table, paginatedData.data);

    // Update pagination
    const oldPagination = container.querySelector('.pagination-container');
    const newPagination = this.createPagination(paginatedData.currentPage, paginatedData.totalPages, allData);
    container.replaceChild(newPagination, oldPagination);
  }

  // Filter methods
  applyColumnFilter(columnKey, filterValue) {
    const container = document.querySelector('.data-table-container');
    const originalData = container.originalData;

    // Get all current filter values
    const filters = {};
    container.querySelectorAll('.filter-select, .filter-input').forEach(filter => {
      const column = filter.getAttribute('data-column');
      const value = filter.value.trim();
      if (value) {
        filters[column] = value;
      }
    });

    // Apply filters
    let filteredData = originalData.filter(row => {
      return Object.keys(filters).every(key => {
        const filterValue = filters[key].toLowerCase();
        const rowValue = (row[key] || '').toString().toLowerCase();
        return rowValue.includes(filterValue);
      });
    });

    container.filteredData = filteredData;

    // Reset to page 1 and update table
    const paginatedData = this.paginateData(filteredData, 1, container.itemsPerPage);
    this.updateTableWithPagination(container, paginatedData, filteredData);
    container.currentPage = 1;
  }

  clearAllFilters() {
    const container = document.querySelector('.data-table-container');

    // Clear all filter inputs
    container.querySelectorAll('.filter-select').forEach(select => select.value = '');
    container.querySelectorAll('.filter-input').forEach(input => input.value = '');
    container.querySelector('.quick-search').value = '';

    // Reset to original data
    container.filteredData = container.originalData;
    const paginatedData = this.paginateData(container.originalData, 1, container.itemsPerPage);
    this.updateTableWithPagination(container, paginatedData, container.originalData);
    container.currentPage = 1;
  }

  addNewRecord() {
    UIComponents.showToast('Add New Production Plan feature', 'info');
  }

  // Simple helper methods
  filterTable(filter) {
    UIComponents.showToast(`Filtering by: ${filter}`, 'info');
  }

  quickSearch(query) {
    console.log('Searching for:', query);
  }

  // Pagination methods
  paginateData(data, page, itemsPerPage) {
    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedData = data.slice(startIndex, endIndex);

    return {
      data: paginatedData,
      currentPage: page,
      totalPages: Math.ceil(data.length / itemsPerPage),
      totalItems: data.length,
      itemsPerPage: itemsPerPage
    };
  }

  createPagination(currentPage, totalPages, allData) {
    const paginationContainer = document.createElement('div');
    paginationContainer.className = 'pagination-container';

    const paginationInfo = document.createElement('div');
    paginationInfo.className = 'pagination-info';
    const startItem = ((currentPage - 1) * 10) + 1;
    const endItem = Math.min(currentPage * 10, allData.length);
    paginationInfo.textContent = `Showing ${startItem}-${endItem} of ${allData.length} entries`;

    const paginationControls = document.createElement('div');
    paginationControls.className = 'pagination-controls';

    // Previous button
    const prevBtn = document.createElement('button');
    prevBtn.className = 'pagination-btn';
    prevBtn.innerHTML = '<span class="material-icons">chevron_left</span> Previous';
    prevBtn.disabled = currentPage === 1;

    // Page numbers
    const pageNumbers = document.createElement('div');
    pageNumbers.className = 'page-numbers';

    for (let i = 1; i <= Math.min(5, totalPages); i++) {
      const pageBtn = document.createElement('button');
      pageBtn.className = `page-btn ${i === currentPage ? 'active' : ''}`;
      pageBtn.textContent = i;
      pageNumbers.appendChild(pageBtn);
    }

    // Next button
    const nextBtn = document.createElement('button');
    nextBtn.className = 'pagination-btn';
    nextBtn.innerHTML = 'Next <span class="material-icons">chevron_right</span>';
    nextBtn.disabled = currentPage === totalPages;

    paginationControls.appendChild(prevBtn);
    paginationControls.appendChild(pageNumbers);
    paginationControls.appendChild(nextBtn);

    paginationContainer.appendChild(paginationInfo);
    paginationContainer.appendChild(paginationControls);

    return paginationContainer;
  }

  // Create column-specific filters
  createColumnFilters(columns) {
    const filtersContainer = document.createElement('div');
    filtersContainer.className = 'column-filters';

    const filtersTitle = document.createElement('div');
    filtersTitle.className = 'filters-title';
    filtersTitle.innerHTML = '<span class="material-icons">filter_list</span> Column Filters';
    filtersContainer.appendChild(filtersTitle);

    const filtersRow = document.createElement('div');
    filtersRow.className = 'filters-row';

    // Add filters for key columns
    const filterableColumns = [
      { key: 'status', title: 'Status', type: 'select', options: ['All', 'Kitting Completed', 'In Progress', 'Pending', 'Quality Check', 'On Hold', 'Cancelled'] },
      { key: 'market', title: 'Market', type: 'select', options: ['All', 'DOMESTIC', 'EXPORT'] },
      { key: 'model', title: 'Model', type: 'text' },
      { key: 'cabin', title: 'Cabin', type: 'select', options: ['All', 'AC', 'NON-AC'] },
      { key: 'revisionNo', title: 'Revision', type: 'select', options: ['All', '1', '2', '3', '4', '5'] }
    ];

    filterableColumns.forEach(column => {
      const filterGroup = document.createElement('div');
      filterGroup.className = 'filter-group';

      const label = document.createElement('label');
      label.textContent = column.title;
      label.className = 'filter-label';
      filterGroup.appendChild(label);

      if (column.type === 'select') {
        const select = document.createElement('select');
        select.className = 'filter-select';

        column.options.forEach(option => {
          const optionElement = document.createElement('option');
          optionElement.value = option === 'All' ? '' : option;
          optionElement.textContent = option;
          select.appendChild(optionElement);
        });

        filterGroup.appendChild(select);
      } else {
        const input = document.createElement('input');
        input.type = 'text';
        input.className = 'filter-input';
        input.placeholder = `Filter ${column.title}...`;
        filterGroup.appendChild(input);
      }

      filtersRow.appendChild(filterGroup);
    });

    // Clear filters button
    const clearBtn = document.createElement('button');
    clearBtn.className = 'clear-filters-btn';
    clearBtn.innerHTML = '<span class="material-icons">clear</span> Clear';
    filtersRow.appendChild(clearBtn);

    filtersContainer.appendChild(filtersRow);
    return filtersContainer;
  }
  }

  // UX Enhancement Methods
  viewRowDetails(row) {
    const modal = UIComponents.createModal(
      'Production Plan Details',
      this.createDetailView(row),
      [
        UIComponents.createButton('Edit', 'primary', 'edit', () => this.quickEditRow(row)),
        UIComponents.createButton('Close', 'secondary', null, () => UIComponents.closeModal(modal))
      ]
    );
    UIComponents.showModal(modal);
  }

  quickEditRow(row) {
    const modal = UIComponents.createModal(
      'Quick Edit',
      this.createQuickEditForm(row),
      [
        UIComponents.createButton('Save', 'primary', 'save', () => this.saveQuickEdit(row)),
        UIComponents.createButton('Cancel', 'secondary', null, () => UIComponents.closeModal(modal))
      ]
    );
    UIComponents.showModal(modal);
  }

  deleteRow(row) {
    if (confirm(`Are you sure you want to delete order ${row.orderNumber}?`)) {
      // Remove from data and refresh table
      const index = window.mockData.productionPlans.findIndex(p => p.id === row.id);
      if (index > -1) {
        window.mockData.productionPlans.splice(index, 1);
        this.loadProductionPlan(); // Refresh table
        UIComponents.showToast('Order deleted successfully', 'success');
      }
    }
  }

  filterTable(filter) {
    // Implement table filtering
    UIComponents.showToast(`Filtering by: ${filter}`, 'info');
  }

  quickSearch(query) {
    // Implement quick search
    console.log('Searching for:', query);
  }

  showBulkActions() {
    UIComponents.showToast('Bulk actions menu', 'info');
  }

  createDetailView(row) {
    return `
      <div class="detail-view">
        <div class="detail-row"><strong>Order Number:</strong> ${row.orderNumber}</div>
        <div class="detail-row"><strong>Model:</strong> ${row.model}</div>
        <div class="detail-row"><strong>Machine Number:</strong> ${row.machineNumber}</div>
        <div class="detail-row"><strong>Market:</strong> ${row.market}</div>
        <div class="detail-row"><strong>Status:</strong> ${row.status}</div>
        <div class="detail-row"><strong>Production Start:</strong> ${row.productionStart}</div>
      </div>
    `;
  }

  createQuickEditForm(row) {
    return `
      <div class="quick-edit-form">
        <div class="form-row">
          <label>Status:</label>
          <select id="edit-status">
            <option value="Kitting Completed" ${row.status === 'Kitting Completed' ? 'selected' : ''}>Kitting Completed</option>
            <option value="In Progress" ${row.status === 'In Progress' ? 'selected' : ''}>In Progress</option>
            <option value="Pending" ${row.status === 'Pending' ? 'selected' : ''}>Pending</option>
            <option value="Quality Check" ${row.status === 'Quality Check' ? 'selected' : ''}>Quality Check</option>
          </select>
        </div>
        <div class="form-row">
          <label>Production Start:</label>
          <input type="date" id="edit-date" value="${this.formatDateForInput(row.productionStart)}">
        </div>
      </div>
    `;
  }

  saveQuickEdit(row) {
    const status = document.getElementById('edit-status').value;
    const date = document.getElementById('edit-date').value;

    // Update the row data
    row.status = status;
    row.productionStart = this.formatDateForDisplay(date);

    // Refresh the table
    this.loadProductionPlan();
    UIComponents.showToast('Changes saved successfully', 'success');
  }

  formatDateForInput(dateStr) {
    // Convert DD/MM/YYYY to YYYY-MM-DD
    const parts = dateStr.split('/');
    if (parts.length === 3) {
      return `20${parts[2]}-${parts[1].padStart(2, '0')}-${parts[0].padStart(2, '0')}`;
    }
    return dateStr;
  }

  formatDateForDisplay(dateStr) {
    // Convert YYYY-MM-DD to DD/MM/YY
    const date = new Date(dateStr);
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear().toString().substr(-2);
    return `${day}/${month}/${year}`;
  }

  loadComingSoon(page) {
    const contentArea = document.getElementById('contentArea');
    contentArea.innerHTML = `
      <div class="coming-soon">
        <span class="material-icons">construction</span>
        <h2>${page.replace('-', ' ').toUpperCase()} Coming Soon</h2>
        <p>This feature is under development and will be available soon.</p>
      </div>
    `;
  }

  async loadInitialData() {
    try {
      // Load initial data from API or use mock data
      this.currentData = window.mockData;
    } catch (error) {
      console.error('Error loading initial data:', error);
      UIComponents.showToast('Error loading data', 'error');
    }
  }

  hideLoadingSpinner() {
    const loadingSpinner = document.getElementById('loading-spinner');
    if (loadingSpinner) {
      setTimeout(() => {
        loadingSpinner.classList.add('hidden');
      }, 500);
    }
  }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  try {
    console.log('Initializing KIPL App...');
    window.kiplApp = new KIPLApp();
    console.log('KIPL App initialized successfully');
  } catch (error) {
    console.error('Error initializing KIPL App:', error);
    // Fallback initialization
    document.getElementById('contentArea').innerHTML = `
      <div style="padding: 20px;">
        <h2>KIPL Warehouse Management System</h2>
        <p>Loading application...</p>
      </div>
    `;
  }
});
