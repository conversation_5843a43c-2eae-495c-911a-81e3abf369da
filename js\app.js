/**
 * Main Application Logic for KIPL Warehouse Management System
 * Modern, responsive UI with headless architecture
 */

class KIPLApp {
  constructor() {
    this.currentPage = 'dashboard';
    this.sidebarCollapsed = false;
    this.currentData = {};
    this.init();
  }

  init() {
    this.setupEventListeners();
    this.loadInitialData();
    this.hideLoadingSpinner();
    this.navigateToPage('production-plan'); // Start with production plan
  }

  setupEventListeners() {
    // Sidebar toggle
    const sidebarToggle = document.getElementById('sidebarToggle');
    if (sidebarToggle) {
      sidebarToggle.addEventListener('click', () => this.toggleSidebar());
    }

    // Navigation items
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
      item.addEventListener('click', (e) => {
        e.preventDefault();
        const page = item.getAttribute('data-page');
        this.navigateToPage(page);
      });
    });

    // User profile dropdown
    const userProfile = document.getElementById('userProfile');
    if (userProfile) {
      userProfile.addEventListener('click', () => this.toggleUserMenu());
    }

    // Modal overlay
    const modalOverlay = document.getElementById('modal-overlay');
    if (modalOverlay) {
      modalOverlay.addEventListener('click', () => {
        const modals = document.querySelectorAll('.modal.show');
        modals.forEach(modal => UIComponents.closeModal(modal));
      });
    }

    // Responsive sidebar
    this.setupResponsiveSidebar();
  }

  setupResponsiveSidebar() {
    const mediaQuery = window.matchMedia('(max-width: 1024px)');
    
    const handleMediaQuery = (e) => {
      const sidebar = document.getElementById('sidebar');
      if (e.matches) {
        // Mobile view
        sidebar.classList.remove('collapsed');
        this.sidebarCollapsed = false;
      }
    };

    mediaQuery.addListener(handleMediaQuery);
    handleMediaQuery(mediaQuery);
  }

  toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    this.sidebarCollapsed = !this.sidebarCollapsed;
    
    if (this.sidebarCollapsed) {
      sidebar.classList.add('collapsed');
    } else {
      sidebar.classList.remove('collapsed');
    }
  }

  toggleUserMenu() {
    // Implement user menu dropdown
    UIComponents.showToast('User menu clicked', 'info');
  }

  navigateToPage(page) {
    // Update active navigation
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
      item.classList.remove('active');
      if (item.getAttribute('data-page') === page) {
        item.classList.add('active');
      }
    });

    // Update page title and breadcrumb
    this.updatePageHeader(page);

    // Load page content
    this.loadPageContent(page);
    this.currentPage = page;
  }

  updatePageHeader(page) {
    const pageTitle = document.getElementById('pageTitle');
    const breadcrumb = document.getElementById('breadcrumb');
    
    const pageConfig = {
      'dashboard': { title: 'Dashboard', breadcrumb: ['Home', 'Dashboard'] },
      'production-plan': { title: 'Production Plan', breadcrumb: ['Home', 'Production Plan'] },
      'firm-order': { title: 'Firm Order', breadcrumb: ['Home', 'Firm Order'] },
      'logistics': { title: 'Logistics', breadcrumb: ['Home', 'Logistics'] },
      'manufacturing': { title: 'Manufacturing', breadcrumb: ['Home', 'Manufacturing'] },
      'warehouse': { title: 'Warehouse Management', breadcrumb: ['Home', 'Warehouse'] },
      'reports': { title: 'Reports', breadcrumb: ['Home', 'Reports'] },
      'settings': { title: 'Settings', breadcrumb: ['Home', 'Settings'] },
      'archive': { title: 'Archive', breadcrumb: ['Home', 'Archive'] }
    };

    const config = pageConfig[page] || { title: 'KIPL WMS', breadcrumb: ['Home'] };
    
    if (pageTitle) {
      pageTitle.textContent = config.title;
    }
    
    if (breadcrumb) {
      breadcrumb.innerHTML = config.breadcrumb.map((item, index) => {
        if (index === config.breadcrumb.length - 1) {
          return `<span>${item}</span>`;
        }
        return `<span>${item}</span><span class="material-icons">chevron_right</span>`;
      }).join('');
    }
  }

  async loadPageContent(page) {
    const contentArea = document.getElementById('contentArea');
    if (!contentArea) return;

    // Show loading
    contentArea.innerHTML = '<div class="loading-content">Loading...</div>';

    try {
      switch (page) {
        case 'dashboard':
          await this.loadDashboard();
          break;
        case 'production-plan':
          await this.loadProductionPlan();
          break;
        case 'firm-order':
          await this.loadFirmOrder();
          break;
        default:
          this.loadComingSoon(page);
      }
    } catch (error) {
      console.error('Error loading page content:', error);
      UIComponents.showToast('Error loading page content', 'error');
    }
  }

  async loadProductionPlan() {
    const contentArea = document.getElementById('contentArea');

    // Define table columns exactly matching original KIPL design
    const columns = [
      {
        key: 'actions',
        title: '',
        render: (value, row) => this.createActionIcons(row)
      },
      { key: 'status', title: 'Status', render: (value, row) => UIComponents.createStatusBadge(value, row.priority) },
      { key: 'slNo', title: 'Sl No' },
      { key: 'market', title: 'Market' },
      { key: 'prodMonth', title: 'Prod Month' },
      { key: 'model', title: 'Model' },
      { key: 'machineNumber', title: 'Machine #' },
      { key: 'specPattern', title: 'SPEC Pattern' },
      { key: 'orderNumber', title: 'Order #' },
      { key: 'cabin', title: 'Cabin' },
      { key: 'arm', title: 'Arm' },
      { key: 'bucket', title: 'Bucket' },
      { key: 'cgms', title: 'CGMS' },
      { key: 'meshGuard', title: 'Mesh Guard / Add' },
      { key: 'productionStart', title: 'Production Start Date' }
    ];

    // Create enhanced data table with UX improvements
    const dataTable = this.createEnhancedDataTable(
      window.mockData.productionPlans,
      columns,
      {
        title: 'Production Plan',
        showSearch: true,
        showFilters: true,
        enableQuickEdit: true,
        enableBulkActions: true
      }
    );

    contentArea.innerHTML = '';
    contentArea.appendChild(dataTable);
  }

  createActionIcons(row) {
    const container = document.createElement('div');
    container.className = 'action-icons';

    // View icon
    const viewIcon = document.createElement('span');
    viewIcon.className = 'material-icons action-icon';
    viewIcon.textContent = 'visibility';
    viewIcon.title = 'View Details';
    viewIcon.onclick = () => this.viewRowDetails(row);

    // Edit icon
    const editIcon = document.createElement('span');
    editIcon.className = 'material-icons action-icon';
    editIcon.textContent = 'edit';
    editIcon.title = 'Quick Edit';
    editIcon.onclick = () => this.quickEditRow(row);

    // Delete icon
    const deleteIcon = document.createElement('span');
    deleteIcon.className = 'material-icons action-icon';
    deleteIcon.textContent = 'delete';
    deleteIcon.title = 'Delete';
    deleteIcon.onclick = () => this.deleteRow(row);

    container.appendChild(viewIcon);
    container.appendChild(editIcon);
    container.appendChild(deleteIcon);

    return container;
  }

  async loadFirmOrder() {
    const contentArea = document.getElementById('contentArea');
    
    // Create a similar table for firm orders
    const columns = [
      { key: 'status', title: 'Status', render: (value, row) => UIComponents.createStatusBadge(value, row.priority) },
      { key: 'market', title: 'Market' },
      { key: 'model', title: 'Model' },
      { key: 'orderNumber', title: 'Order #' },
      { key: 'cabin', title: 'Cabin' },
      { key: 'productionStart', title: 'Start Date' }
    ];

    const dataTable = UIComponents.createDataTable(
      window.mockData.productionPlans.slice(0, 3), // Use subset for firm orders
      columns,
      {
        title: 'Firm Orders',
        showAddButton: true
      }
    );

    contentArea.innerHTML = '';
    contentArea.appendChild(dataTable);
  }

  async loadDashboard() {
    const contentArea = document.getElementById('contentArea');
    
    // Create dashboard layout
    const dashboard = document.createElement('div');
    dashboard.className = 'dashboard';
    
    // KPI Cards
    const kpiContainer = document.createElement('div');
    kpiContainer.className = 'kpi-container';
    
    const kpis = [
      { title: 'Total Orders', value: '156', icon: 'assignment', color: 'primary' },
      { title: 'Completed', value: '89', icon: 'check_circle', color: 'success' },
      { title: 'Pending', value: '45', icon: 'pending', color: 'warning' },
      { title: 'Delayed', value: '22', icon: 'error', color: 'error' }
    ];

    kpis.forEach(kpi => {
      const kpiCard = this.createKPICard(kpi);
      kpiContainer.appendChild(kpiCard);
    });

    dashboard.appendChild(kpiContainer);
    
    // Recent activities
    const recentSection = document.createElement('div');
    recentSection.className = 'dashboard-section';
    recentSection.innerHTML = `
      <h3>Recent Production Activities</h3>
      <div class="activity-list">
        <div class="activity-item">
          <span class="material-icons">check_circle</span>
          <span>Production completed for Order #S86NSS64D</span>
          <span class="activity-time">2 hours ago</span>
        </div>
        <div class="activity-item">
          <span class="material-icons">warning</span>
          <span>Delay reported for Order #E45ABC123</span>
          <span class="activity-time">4 hours ago</span>
        </div>
      </div>
    `;

    dashboard.appendChild(recentSection);
    
    contentArea.innerHTML = '';
    contentArea.appendChild(dashboard);
  }

  createKPICard(kpi) {
    const card = document.createElement('div');
    card.className = `kpi-card kpi-${kpi.color}`;
    
    card.innerHTML = `
      <div class="kpi-icon">
        <span class="material-icons">${kpi.icon}</span>
      </div>
      <div class="kpi-content">
        <div class="kpi-value">${kpi.value}</div>
        <div class="kpi-title">${kpi.title}</div>
      </div>
    `;
    
    return card;
  }

  // Enhanced Data Table with UX improvements
  createEnhancedDataTable(data, columns, options = {}) {
    const container = document.createElement('div');
    container.className = 'data-table-container';

    // Enhanced header with quick actions
    const header = document.createElement('div');
    header.className = 'table-header';

    const leftSection = document.createElement('div');
    leftSection.style.display = 'flex';
    leftSection.style.alignItems = 'center';
    leftSection.style.gap = '12px';

    const title = document.createElement('span');
    title.className = 'table-title';
    title.textContent = options.title || 'Data Table';
    leftSection.appendChild(title);

    // Quick filter buttons
    if (options.showFilters) {
      const filterBtns = ['All', 'Completed', 'In Progress', 'Pending'];
      filterBtns.forEach(filter => {
        const btn = document.createElement('button');
        btn.className = 'filter-btn';
        btn.textContent = filter;
        btn.onclick = () => this.filterTable(filter);
        leftSection.appendChild(btn);
      });
    }

    const rightSection = document.createElement('div');
    rightSection.style.display = 'flex';
    rightSection.style.alignItems = 'center';
    rightSection.style.gap = '8px';

    // Quick search
    if (options.showSearch) {
      const searchInput = document.createElement('input');
      searchInput.type = 'text';
      searchInput.placeholder = 'Quick search...';
      searchInput.className = 'quick-search';
      searchInput.oninput = (e) => this.quickSearch(e.target.value);
      rightSection.appendChild(searchInput);
    }

    // Bulk actions
    if (options.enableBulkActions) {
      const bulkBtn = document.createElement('button');
      bulkBtn.className = 'bulk-action-btn';
      bulkBtn.textContent = 'Bulk Actions';
      bulkBtn.onclick = () => this.showBulkActions();
      rightSection.appendChild(bulkBtn);
    }

    header.appendChild(leftSection);
    header.appendChild(rightSection);
    container.appendChild(header);

    // Enhanced table with original KIPL styling
    const table = UIComponents.createDataTable(data, columns, { showHeader: false });
    container.appendChild(table);

    return container;
  }

  // UX Enhancement Methods
  viewRowDetails(row) {
    const modal = UIComponents.createModal(
      'Production Plan Details',
      this.createDetailView(row),
      [
        UIComponents.createButton('Edit', 'primary', 'edit', () => this.quickEditRow(row)),
        UIComponents.createButton('Close', 'secondary', null, () => UIComponents.closeModal(modal))
      ]
    );
    UIComponents.showModal(modal);
  }

  quickEditRow(row) {
    const modal = UIComponents.createModal(
      'Quick Edit',
      this.createQuickEditForm(row),
      [
        UIComponents.createButton('Save', 'primary', 'save', () => this.saveQuickEdit(row)),
        UIComponents.createButton('Cancel', 'secondary', null, () => UIComponents.closeModal(modal))
      ]
    );
    UIComponents.showModal(modal);
  }

  deleteRow(row) {
    if (confirm(`Are you sure you want to delete order ${row.orderNumber}?`)) {
      // Remove from data and refresh table
      const index = window.mockData.productionPlans.findIndex(p => p.id === row.id);
      if (index > -1) {
        window.mockData.productionPlans.splice(index, 1);
        this.loadProductionPlan(); // Refresh table
        UIComponents.showToast('Order deleted successfully', 'success');
      }
    }
  }

  filterTable(filter) {
    // Implement table filtering
    UIComponents.showToast(`Filtering by: ${filter}`, 'info');
  }

  quickSearch(query) {
    // Implement quick search
    console.log('Searching for:', query);
  }

  showBulkActions() {
    UIComponents.showToast('Bulk actions menu', 'info');
  }

  createDetailView(row) {
    return `
      <div class="detail-view">
        <div class="detail-row"><strong>Order Number:</strong> ${row.orderNumber}</div>
        <div class="detail-row"><strong>Model:</strong> ${row.model}</div>
        <div class="detail-row"><strong>Machine Number:</strong> ${row.machineNumber}</div>
        <div class="detail-row"><strong>Market:</strong> ${row.market}</div>
        <div class="detail-row"><strong>Status:</strong> ${row.status}</div>
        <div class="detail-row"><strong>Production Start:</strong> ${row.productionStart}</div>
      </div>
    `;
  }

  createQuickEditForm(row) {
    return `
      <div class="quick-edit-form">
        <div class="form-row">
          <label>Status:</label>
          <select id="edit-status">
            <option value="Kitting Completed" ${row.status === 'Kitting Completed' ? 'selected' : ''}>Kitting Completed</option>
            <option value="In Progress" ${row.status === 'In Progress' ? 'selected' : ''}>In Progress</option>
            <option value="Pending" ${row.status === 'Pending' ? 'selected' : ''}>Pending</option>
            <option value="Quality Check" ${row.status === 'Quality Check' ? 'selected' : ''}>Quality Check</option>
          </select>
        </div>
        <div class="form-row">
          <label>Production Start:</label>
          <input type="date" id="edit-date" value="${this.formatDateForInput(row.productionStart)}">
        </div>
      </div>
    `;
  }

  saveQuickEdit(row) {
    const status = document.getElementById('edit-status').value;
    const date = document.getElementById('edit-date').value;

    // Update the row data
    row.status = status;
    row.productionStart = this.formatDateForDisplay(date);

    // Refresh the table
    this.loadProductionPlan();
    UIComponents.showToast('Changes saved successfully', 'success');
  }

  formatDateForInput(dateStr) {
    // Convert DD/MM/YYYY to YYYY-MM-DD
    const parts = dateStr.split('/');
    if (parts.length === 3) {
      return `20${parts[2]}-${parts[1].padStart(2, '0')}-${parts[0].padStart(2, '0')}`;
    }
    return dateStr;
  }

  formatDateForDisplay(dateStr) {
    // Convert YYYY-MM-DD to DD/MM/YY
    const date = new Date(dateStr);
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear().toString().substr(-2);
    return `${day}/${month}/${year}`;
  }

  loadComingSoon(page) {
    const contentArea = document.getElementById('contentArea');
    contentArea.innerHTML = `
      <div class="coming-soon">
        <span class="material-icons">construction</span>
        <h2>${page.replace('-', ' ').toUpperCase()} Coming Soon</h2>
        <p>This feature is under development and will be available soon.</p>
      </div>
    `;
  }

  async loadInitialData() {
    try {
      // Load initial data from API or use mock data
      this.currentData = window.mockData;
    } catch (error) {
      console.error('Error loading initial data:', error);
      UIComponents.showToast('Error loading data', 'error');
    }
  }

  hideLoadingSpinner() {
    const loadingSpinner = document.getElementById('loading-spinner');
    if (loadingSpinner) {
      setTimeout(() => {
        loadingSpinner.classList.add('hidden');
      }, 500);
    }
  }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.kiplApp = new KIPLApp();
});
