/**
 * UI Components for KIPL Warehouse Management System
 * Modern, reusable components with Material Design principles
 */

class UIComponents {
  // Create status badge component
  static createStatusBadge(status, priority = 'medium') {
    const badge = document.createElement('span');
    badge.className = `status-badge status-${status.toLowerCase().replace(/\s+/g, '-')} priority-${priority}`;
    badge.textContent = status;
    return badge;
  }

  // Create action button component
  static createButton(text, type = 'primary', icon = null, onClick = null) {
    const button = document.createElement('button');
    button.className = `btn btn-${type}`;
    
    if (icon) {
      const iconElement = document.createElement('span');
      iconElement.className = 'material-icons';
      iconElement.textContent = icon;
      button.appendChild(iconElement);
    }
    
    if (text) {
      const textElement = document.createElement('span');
      textElement.textContent = text;
      button.appendChild(textElement);
    }
    
    if (onClick) {
      button.addEventListener('click', onClick);
    }
    
    return button;
  }

  // Create card component
  static createCard(title, content, actions = []) {
    const card = document.createElement('div');
    card.className = 'card';
    
    if (title) {
      const header = document.createElement('div');
      header.className = 'card-header';
      header.innerHTML = `<h3 class="card-title">${title}</h3>`;
      card.appendChild(header);
    }
    
    const body = document.createElement('div');
    body.className = 'card-body';
    
    if (typeof content === 'string') {
      body.innerHTML = content;
    } else {
      body.appendChild(content);
    }
    
    card.appendChild(body);
    
    if (actions.length > 0) {
      const footer = document.createElement('div');
      footer.className = 'card-footer';
      actions.forEach(action => footer.appendChild(action));
      card.appendChild(footer);
    }
    
    return card;
  }

  // Create data table component
  static createDataTable(data, columns, options = {}) {
    const container = document.createElement('div');
    container.className = 'data-table-container';
    
    // Create table header with search and filters
    if (options.showHeader !== false) {
      const header = document.createElement('div');
      header.className = 'table-header';
      
      const title = document.createElement('h2');
      title.className = 'table-title';
      title.textContent = options.title || 'Data Table';
      header.appendChild(title);
      
      const actions = document.createElement('div');
      actions.className = 'table-actions';
      
      // Search input
      const searchContainer = document.createElement('div');
      searchContainer.className = 'search-container';
      
      const searchInput = document.createElement('input');
      searchInput.type = 'text';
      searchInput.placeholder = 'Search...';
      searchInput.className = 'search-input';
      
      const searchIcon = document.createElement('span');
      searchIcon.className = 'material-icons search-icon';
      searchIcon.textContent = 'search';
      
      searchContainer.appendChild(searchIcon);
      searchContainer.appendChild(searchInput);
      actions.appendChild(searchContainer);
      
      // Add button
      if (options.showAddButton !== false) {
        const addButton = this.createButton('Add New', 'primary', 'add');
        actions.appendChild(addButton);
      }
      
      header.appendChild(actions);
      container.appendChild(header);
    }
    
    // Create table
    const tableWrapper = document.createElement('div');
    tableWrapper.className = 'table-wrapper';
    
    const table = document.createElement('table');
    table.className = 'data-table';
    
    // Create thead
    const thead = document.createElement('thead');
    const headerRow = document.createElement('tr');
    
    columns.forEach(column => {
      const th = document.createElement('th');
      th.textContent = column.title || column.key;
      th.className = column.sortable !== false ? 'sortable' : '';
      
      if (column.sortable !== false) {
        const sortIcon = document.createElement('span');
        sortIcon.className = 'material-icons sort-icon';
        sortIcon.textContent = 'unfold_more';
        th.appendChild(sortIcon);
      }
      
      headerRow.appendChild(th);
    });
    
    thead.appendChild(headerRow);
    table.appendChild(thead);
    
    // Create tbody
    const tbody = document.createElement('tbody');
    
    data.forEach(row => {
      const tr = document.createElement('tr');
      tr.className = 'table-row';
      
      columns.forEach(column => {
        const td = document.createElement('td');
        
        if (column.render) {
          const rendered = column.render(row[column.key], row);
          if (typeof rendered === 'string') {
            td.innerHTML = rendered;
          } else {
            td.appendChild(rendered);
          }
        } else {
          td.textContent = row[column.key] || '';
        }
        
        tr.appendChild(td);
      });
      
      tbody.appendChild(tr);
    });
    
    table.appendChild(tbody);
    tableWrapper.appendChild(table);
    container.appendChild(tableWrapper);
    
    // Add pagination if needed
    if (options.pagination !== false && data.length > 10) {
      const pagination = this.createPagination(1, Math.ceil(data.length / 10));
      container.appendChild(pagination);
    }
    
    return container;
  }

  // Create pagination component
  static createPagination(currentPage, totalPages) {
    const pagination = document.createElement('div');
    pagination.className = 'pagination';
    
    // Previous button
    const prevButton = document.createElement('button');
    prevButton.className = 'pagination-btn';
    prevButton.innerHTML = '<span class="material-icons">chevron_left</span>';
    prevButton.disabled = currentPage === 1;
    pagination.appendChild(prevButton);
    
    // Page numbers
    const pageNumbers = document.createElement('div');
    pageNumbers.className = 'page-numbers';
    
    for (let i = 1; i <= Math.min(totalPages, 5); i++) {
      const pageBtn = document.createElement('button');
      pageBtn.className = `pagination-btn ${i === currentPage ? 'active' : ''}`;
      pageBtn.textContent = i;
      pageNumbers.appendChild(pageBtn);
    }
    
    pagination.appendChild(pageNumbers);
    
    // Next button
    const nextButton = document.createElement('button');
    nextButton.className = 'pagination-btn';
    nextButton.innerHTML = '<span class="material-icons">chevron_right</span>';
    nextButton.disabled = currentPage === totalPages;
    pagination.appendChild(nextButton);
    
    return pagination;
  }

  // Create modal component
  static createModal(title, content, actions = []) {
    const modal = document.createElement('div');
    modal.className = 'modal';
    
    const modalContent = document.createElement('div');
    modalContent.className = 'modal-content';
    
    // Modal header
    const header = document.createElement('div');
    header.className = 'modal-header';
    
    const titleElement = document.createElement('h2');
    titleElement.className = 'modal-title';
    titleElement.textContent = title;
    
    const closeButton = document.createElement('button');
    closeButton.className = 'modal-close';
    closeButton.innerHTML = '<span class="material-icons">close</span>';
    closeButton.addEventListener('click', () => this.closeModal(modal));
    
    header.appendChild(titleElement);
    header.appendChild(closeButton);
    modalContent.appendChild(header);
    
    // Modal body
    const body = document.createElement('div');
    body.className = 'modal-body';
    
    if (typeof content === 'string') {
      body.innerHTML = content;
    } else {
      body.appendChild(content);
    }
    
    modalContent.appendChild(body);
    
    // Modal footer
    if (actions.length > 0) {
      const footer = document.createElement('div');
      footer.className = 'modal-footer';
      actions.forEach(action => footer.appendChild(action));
      modalContent.appendChild(footer);
    }
    
    modal.appendChild(modalContent);
    
    return modal;
  }

  // Show modal
  static showModal(modal) {
    document.body.appendChild(modal);
    modal.classList.add('show');
    document.getElementById('modal-overlay').classList.remove('hidden');
  }

  // Close modal
  static closeModal(modal) {
    modal.classList.remove('show');
    document.getElementById('modal-overlay').classList.add('hidden');
    setTimeout(() => {
      if (modal.parentNode) {
        modal.parentNode.removeChild(modal);
      }
    }, 300);
  }

  // Create loading spinner
  static createLoadingSpinner(size = 'medium') {
    const spinner = document.createElement('div');
    spinner.className = `loading-spinner size-${size}`;
    
    const spinnerElement = document.createElement('div');
    spinnerElement.className = 'spinner';
    
    spinner.appendChild(spinnerElement);
    return spinner;
  }

  // Create toast notification
  static showToast(message, type = 'info', duration = 3000) {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    
    const icon = document.createElement('span');
    icon.className = 'material-icons toast-icon';
    
    switch (type) {
      case 'success':
        icon.textContent = 'check_circle';
        break;
      case 'error':
        icon.textContent = 'error';
        break;
      case 'warning':
        icon.textContent = 'warning';
        break;
      default:
        icon.textContent = 'info';
    }
    
    const messageElement = document.createElement('span');
    messageElement.className = 'toast-message';
    messageElement.textContent = message;
    
    const closeButton = document.createElement('button');
    closeButton.className = 'toast-close';
    closeButton.innerHTML = '<span class="material-icons">close</span>';
    closeButton.addEventListener('click', () => this.removeToast(toast));
    
    toast.appendChild(icon);
    toast.appendChild(messageElement);
    toast.appendChild(closeButton);
    
    // Add to toast container
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
      toastContainer = document.createElement('div');
      toastContainer.id = 'toast-container';
      toastContainer.className = 'toast-container';
      document.body.appendChild(toastContainer);
    }
    
    toastContainer.appendChild(toast);
    
    // Auto remove after duration
    setTimeout(() => {
      this.removeToast(toast);
    }, duration);
    
    return toast;
  }

  // Remove toast
  static removeToast(toast) {
    toast.classList.add('removing');
    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
    }, 300);
  }
}

// Export for global use
window.UIComponents = UIComponents;
