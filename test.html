<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>KIPL Test - Simple Version</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <!-- Material Icons -->
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #F2F2F2;
    }
    
    .container {
      background: white;
      border: 2px solid #8EA9DB;
      border-radius: 4px;
      overflow: hidden;
    }
    
    .header {
      background: linear-gradient(135deg, #D9E2F3, #F0F8FF);
      padding: 12px 16px;
      border-bottom: 2px solid #8EA9DB;
      font-weight: bold;
      font-size: 14px;
    }
    
    table {
      width: 100%;
      border-collapse: separate;
      border-spacing: 0;
      font-size: 12px;
    }
    
    th {
      background: linear-gradient(135deg, #D9E2F3, #F0F8FF);
      padding: 8px 6px;
      text-align: center;
      font-weight: bold;
      border: 1px solid #8EA9DB;
      font-size: 12px;
    }
    
    td {
      padding: 6px 4px;
      border: 1px solid #8EA9DB;
      text-align: center;
      font-size: 12px;
    }
    
    tr:nth-child(even) {
      background: #F2F2F2;
    }
    
    tr:nth-child(odd) {
      background: #FFFFFF;
    }
    
    .status-badge {
      display: inline-block;
      padding: 3px 8px;
      border-radius: 0;
      font-size: 12px;
      font-weight: normal;
      text-align: center;
      min-width: 100px;
    }
    
    .status-kitting-completed {
      background: #70AD47;
      color: white;
    }
    
    .status-in-progress {
      background: #4472C4;
      color: white;
    }
    
    .status-pending {
      background: #FFC000;
      color: black;
    }
    
    .status-quality-check {
      background: #7030A0;
      color: white;
    }
    
    .status-on-hold {
      background: #FF6B35;
      color: white;
    }
    
    .status-cancelled {
      background: #DC3545;
      color: white;
    }
    
    .action-icons {
      display: flex;
      gap: 4px;
      justify-content: center;
    }
    
    .action-icon {
      font-size: 14px;
      cursor: pointer;
      color: #2F5597;
      padding: 2px;
    }
    
    .action-icon:hover {
      background: rgba(47, 85, 151, 0.1);
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      Production Plan
    </div>
    
    <table>
      <thead>
        <tr>
          <th>Actions</th>
          <th>Status</th>
          <th>Revision #</th>
          <th>Market</th>
          <th>Prod Month</th>
          <th>Model</th>
          <th>Machine #</th>
          <th>SPEC Pattern</th>
          <th>Order #</th>
          <th>Cabin</th>
          <th>Arm</th>
          <th>Bucket</th>
          <th>DGMS</th>
          <th>Mesh Guard / Add</th>
          <th>Production Start Date</th>
        </tr>
      </thead>
      <tbody id="tableBody">
        <!-- Data will be inserted here -->
      </tbody>
    </table>
  </div>

  <script>
    // Test data
    const testData = [
      {
        id: 1,
        status: 'Kitting Completed',
        revisionNo: 1,
        market: 'DOMESTIC',
        prodMonth: 'Sep 19',
        model: 'PC130-NI@-7',
        machineNumber: 'N521950',
        specPattern: 'NAC',
        orderNumber: 'S86NSS64D',
        cabin: 'NON-AC',
        arm: '2.1M',
        bucket: '0.64_CUM_NON-DGMS',
        dgms: '',
        meshGuard: 'Mesh Guard',
        productionStart: '28/08/2019'
      },
      {
        id: 2,
        status: 'In Progress',
        revisionNo: 2,
        market: 'DOMESTIC',
        prodMonth: 'Sep 19',
        model: 'PC130-NI@-7',
        machineNumber: 'N521951',
        specPattern: 'NCA',
        orderNumber: 'S86NSS64D',
        cabin: 'NON-AC',
        arm: '2.1M TATT',
        bucket: '0.64_CUM_NON-DGMS',
        dgms: '',
        meshGuard: 'Mesh Guard',
        productionStart: '29/08/2019'
      },
      {
        id: 3,
        status: 'Pending',
        revisionNo: 1,
        market: 'DOMESTIC',
        prodMonth: 'Sep 19',
        model: 'PC130-NI@-7',
        machineNumber: 'N521952',
        specPattern: 'NAC',
        orderNumber: 'S86NSS64D',
        cabin: 'NON-AC',
        arm: '2.1M TATT',
        bucket: '0.64_CUM_NON-DGMS',
        dgms: '',
        meshGuard: 'Mesh Guard',
        productionStart: '30/08/2019'
      }
    ];

    function createStatusBadge(status) {
      const statusClass = status.toLowerCase().replace(/\s+/g, '-');
      return `<span class="status-badge status-${statusClass}">${status}</span>`;
    }

    function createActionIcons() {
      return `
        <div class="action-icons">
          <span class="material-icons action-icon" title="View Details">visibility</span>
          <span class="material-icons action-icon" title="Edit">edit</span>
          <span class="material-icons action-icon" title="Copy">content_copy</span>
          <span class="material-icons action-icon" title="Delete">delete</span>
        </div>
      `;
    }

    function renderTable() {
      const tbody = document.getElementById('tableBody');
      tbody.innerHTML = '';
      
      testData.forEach(row => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
          <td>${createActionIcons()}</td>
          <td>${createStatusBadge(row.status)}</td>
          <td>${row.revisionNo}</td>
          <td>${row.market}</td>
          <td>${row.prodMonth}</td>
          <td>${row.model}</td>
          <td>${row.machineNumber}</td>
          <td>${row.specPattern}</td>
          <td>${row.orderNumber}</td>
          <td>${row.cabin}</td>
          <td>${row.arm}</td>
          <td>${row.bucket}</td>
          <td>${row.dgms}</td>
          <td>${row.meshGuard}</td>
          <td>${row.productionStart}</td>
        `;
        tbody.appendChild(tr);
      });
    }

    // Initialize
    document.addEventListener('DOMContentLoaded', () => {
      console.log('Test page loaded');
      renderTable();
    });
  </script>
</body>
</html>
