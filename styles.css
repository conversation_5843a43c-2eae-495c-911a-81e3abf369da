/* KIPL Warehouse Management System - Exact Replica with Modern UX */

/* CSS Variables matching exact KIPL design from images */
:root {
  /* Exact KIPL Colors from images */
  --kipl-sidebar-blue: #2F5597;
  --kipl-header-orange: #FFA500;
  --kipl-table-header: #D9E2F3;
  --kipl-row-even: #F2F2F2;
  --kipl-row-odd: #FFFFFF;
  --kipl-border: #8EA9DB;
  --kipl-text-dark: #000000;

  /* Status Colors matching original */
  --status-completed: #70AD47;
  --status-progress: #4472C4;
  --status-pending: #FFC000;
  --status-quality: #7030A0;

  /* Background Colors */
  --background-color: #F2F2F2;
  --surface-color: #FFFFFF;
  --sidebar-color: #2F5597;
  --header-orange: #FFA500;

  /* Text Colors */
  --text-primary: #000000;
  --text-secondary: #000000;
  --text-white: #FFFFFF;

  /* Border Colors */
  --border-color: #8EA9DB;
  --border-light: #D9E2F3;
  
  /* Spacing - matching original layout */
  --spacing-xs: 2px;
  --spacing-sm: 4px;
  --spacing-md: 8px;
  --spacing-lg: 12px;
  --spacing-xl: 16px;
  --spacing-2xl: 24px;

  /* Typography - matching original larger fonts */
  --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 13px;
  --font-size-lg: 14px;
  --font-size-xl: 16px;
  --font-size-2xl: 18px;
  --font-size-3xl: 20px;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-full: 9999px;
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.4;
  color: var(--text-primary);
  background-color: var(--background-color);
  margin: 0;
  padding: 0;
}

/* Loading Spinner */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: opacity var(--transition-normal);
}

.loading-overlay.hidden {
  opacity: 0;
  pointer-events: none;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-light);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* App Layout */
.app-container {
  display: flex;
  min-height: 100vh;
  background-color: var(--background-color);
}

/* Sidebar - Exact KIPL Design */
.sidebar {
  width: 200px;
  background: var(--sidebar-color);
  display: flex;
  flex-direction: column;
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 1000;
  transition: transform var(--transition-normal);
}

.sidebar.collapsed {
  width: 50px;
}

.sidebar.collapsed .nav-text,
.sidebar.collapsed .logo-text {
  display: none;
}

.sidebar-header {
  padding: var(--spacing-lg);
  background: var(--sidebar-color);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60px;
  border-bottom: 1px solid rgba(255,255,255,0.2);
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-weight: bold;
  font-size: var(--font-size-xl);
  color: var(--text-white);
  text-decoration: none;
}

.logo-icon {
  width: 32px;
  height: 32px;
  background: var(--text-white);
  color: var(--sidebar-color);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: var(--font-size-lg);
}

.logo-text {
  transition: all var(--transition-normal);
}

.sidebar-toggle {
  background: none;
  border: none;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  cursor: pointer;
  color: var(--text-secondary);
  transition: all var(--transition-fast);
}

.sidebar-toggle:hover {
  background-color: var(--surface-variant);
  color: var(--text-primary);
}

/* Navigation - KIPL Style */
.sidebar-nav {
  flex: 1;
  padding: 0;
  overflow-y: auto;
  background: var(--sidebar-color);
}

.nav-section {
  margin-bottom: 0;
}

.nav-section-title {
  display: none; /* Hide sections in original design */
}

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: 12px 16px;
  text-decoration: none;
  color: var(--text-white);
  font-size: var(--font-size-sm);
  font-weight: normal;
  transition: all 0.2s ease;
  border-bottom: 1px solid rgba(255,255,255,0.1);
  position: relative;
}

.nav-item:hover {
  background-color: rgba(255,255,255,0.1);
  color: var(--text-white);
}

.nav-item.active {
  background: rgba(255,255,255,0.2);
  color: var(--text-white);
  border-left: 4px solid var(--header-orange);
}

.nav-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--header-orange);
}

.nav-item .material-icons {
  font-size: 20px;
  min-width: 20px;
}

.nav-text {
  transition: all var(--transition-normal);
  white-space: nowrap;
}

/* Main Content */
.main-content {
  flex: 1;
  margin-left: 200px;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  transition: margin-left var(--transition-normal);
}

.sidebar.collapsed + .main-content {
  margin-left: 50px;
}

/* Header - KIPL Orange Header */
.app-header {
  background: var(--header-orange);
  border-bottom: 2px solid #E69500;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 50px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: var(--font-size-xl);
  font-weight: bold;
  color: var(--text-white);
  margin: 0;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-xs);
  color: rgba(255,255,255,0.8);
  margin-top: 2px;
}

.breadcrumb .material-icons {
  font-size: 12px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.icon-button {
  position: relative;
  background: none;
  border: none;
  padding: 6px;
  cursor: pointer;
  color: var(--text-white);
  transition: all 0.2s ease;
  border-radius: 3px;
}

.icon-button:hover {
  background-color: rgba(255,255,255,0.1);
}

.icon-button .badge {
  position: absolute;
  top: 2px;
  right: 2px;
  background: #FF4444;
  color: white;
  font-size: 9px;
  font-weight: bold;
  padding: 1px 4px;
  border-radius: 8px;
  min-width: 12px;
  text-align: center;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 3px;
  color: var(--text-white);
}

.user-profile:hover {
  background-color: rgba(255,255,255,0.1);
}

.user-avatar .material-icons {
  font-size: 24px;
  color: var(--text-white);
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: normal;
  font-size: var(--font-size-sm);
  color: var(--text-white);
  line-height: 1.2;
}

.user-role {
  font-size: var(--font-size-xs);
  color: rgba(255,255,255,0.8);
  line-height: 1.2;
}

/* Content Area */
.content-area {
  flex: 1;
  padding: var(--spacing-xl);
  overflow-y: auto;
}

/* Utility Classes */
.hidden {
  display: none !important;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Data Table Styles - Exact KIPL Original Design */
.data-table-container {
  background: var(--surface-color);
  border: 2px solid var(--border-color);
  margin: 4px;
  box-shadow: 1px 1px 3px rgba(0,0,0,0.1);
}

.table-header {
  padding: 6px 8px;
  background: var(--kipl-table-header);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 24px;
}

.table-title {
  font-size: var(--font-size-sm);
  font-weight: bold;
  color: var(--text-primary);
  margin: 0;
}

.table-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  padding: var(--spacing-md) var(--spacing-md) var(--spacing-md) 40px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  width: 250px;
  transition: all var(--transition-fast);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

.search-icon {
  position: absolute;
  left: var(--spacing-md);
  color: var(--text-secondary);
  font-size: 18px;
}

.table-wrapper {
  overflow-x: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--font-size-xs);
}

.data-table th {
  background: var(--kipl-table-header);
  padding: 8px 6px;
  text-align: center;
  font-weight: bold;
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  white-space: nowrap;
  position: relative;
  font-size: var(--font-size-sm);
  vertical-align: middle;
}

.data-table th.sortable {
  cursor: pointer;
  user-select: none;
}

.data-table th.sortable:hover {
  background: #D0E8FF;
}

.sort-icon {
  margin-left: 4px;
  font-size: 12px;
  color: var(--text-secondary);
}

.data-table td {
  padding: 6px 4px;
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  vertical-align: middle;
  text-align: center;
}

.table-row:hover {
  background: #E6F3FF !important;
}

.table-row:nth-child(even) {
  background: var(--kipl-row-even);
}

.table-row:nth-child(odd) {
  background: var(--kipl-row-odd);
}

/* Status Badge Styles - Exact KIPL Original */
.status-badge {
  display: inline-block;
  padding: 3px 8px;
  font-size: var(--font-size-sm);
  font-weight: normal;
  text-align: center;
  border-radius: 0;
  min-width: 100px;
  border: 1px solid transparent;
}

.status-badge.status-kitting-completed {
  background: var(--status-completed);
  color: white;
  border-color: #5A8A3A;
}

.status-badge.status-in-progress {
  background: var(--status-progress);
  color: white;
  border-color: #2F5597;
}

.status-badge.status-pending {
  background: var(--status-pending);
  color: black;
  border-color: #D99000;
}

.status-badge.status-quality-check {
  background: var(--status-quality);
  color: white;
  border-color: #5A1F70;
}

/* Action Icons in Table */
.action-icons {
  display: flex;
  gap: 4px;
  align-items: center;
}

.action-icon {
  width: 16px;
  height: 16px;
  cursor: pointer;
  color: var(--kipl-blue);
  font-size: 14px;
}

.action-icon:hover {
  color: var(--kipl-dark-blue);
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  white-space: nowrap;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--surface-variant);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background: var(--border-light);
}

/* Pagination Styles */
.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
}

.pagination-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: 1px solid var(--border-color);
  background: var(--surface-color);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--font-size-sm);
}

.pagination-btn:hover:not(:disabled) {
  background: var(--surface-variant);
  border-color: var(--primary-color);
}

.pagination-btn.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Dashboard Styles */
.dashboard {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.kpi-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

.kpi-card {
  background: var(--surface-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  transition: all var(--transition-fast);
}

.kpi-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.kpi-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
}

.kpi-card.kpi-primary .kpi-icon {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: white;
}

.kpi-card.kpi-success .kpi-icon {
  background: linear-gradient(135deg, var(--success-color), #66bb6a);
  color: white;
}

.kpi-card.kpi-warning .kpi-icon {
  background: linear-gradient(135deg, var(--warning-color), #ffb74d);
  color: white;
}

.kpi-card.kpi-error .kpi-icon {
  background: linear-gradient(135deg, var(--error-color), #ef5350);
  color: white;
}

.kpi-content {
  flex: 1;
}

.kpi-value {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
}

.kpi-title {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);
}

.dashboard-section {
  background: var(--surface-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
}

.dashboard-section h3 {
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
  font-size: var(--font-size-lg);
  font-weight: 600;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.activity-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  background: var(--surface-variant);
}

.activity-item .material-icons {
  color: var(--primary-color);
}

.activity-time {
  margin-left: auto;
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

/* Coming Soon Styles */
.coming-soon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2xl);
  text-align: center;
  color: var(--text-secondary);
}

.coming-soon .material-icons {
  font-size: 64px;
  margin-bottom: var(--spacing-lg);
  color: var(--text-disabled);
}

.coming-soon h2 {
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

/* Loading Content */
.loading-content {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2xl);
  color: var(--text-secondary);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .sidebar {
    transform: translateX(-100%);
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
  }

  .sidebar.collapsed + .main-content {
    margin-left: 0;
  }

  .kpi-container {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

@media (max-width: 768px) {
  .app-header {
    padding: var(--spacing-md) var(--spacing-lg);
  }

  .page-title {
    font-size: var(--font-size-xl);
  }

  .content-area {
    padding: var(--spacing-lg);
  }

  .user-info {
    display: none;
  }

  .table-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }

  .table-actions {
    justify-content: space-between;
  }

  .search-input {
    width: 200px;
  }

  .kpi-container {
    grid-template-columns: 1fr;
  }
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9998;
  transition: opacity var(--transition-normal);
}

.modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.9);
  z-index: 9999;
  opacity: 0;
  transition: all var(--transition-normal);
}

.modal.show {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

.modal-content {
  background: var(--surface-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  max-width: 500px;
  width: 90vw;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  padding: var(--spacing-lg) var(--spacing-xl);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.modal-close {
  background: none;
  border: none;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  cursor: pointer;
  color: var(--text-secondary);
  transition: all var(--transition-fast);
}

.modal-close:hover {
  background: var(--surface-variant);
  color: var(--text-primary);
}

.modal-body {
  padding: var(--spacing-xl);
  overflow-y: auto;
}

.modal-footer {
  padding: var(--spacing-lg) var(--spacing-xl);
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: var(--spacing-md);
  justify-content: flex-end;
}

/* Toast Styles */
.toast-container {
  position: fixed;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.toast {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--surface-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  min-width: 300px;
  transform: translateX(100%);
  animation: slideIn 0.3s ease-out forwards;
}

.toast.removing {
  animation: slideOut 0.3s ease-in forwards;
}

@keyframes slideIn {
  to {
    transform: translateX(0);
  }
}

@keyframes slideOut {
  to {
    transform: translateX(100%);
  }
}

.toast-success {
  border-left: 4px solid var(--success-color);
}

.toast-error {
  border-left: 4px solid var(--error-color);
}

.toast-warning {
  border-left: 4px solid var(--warning-color);
}

.toast-info {
  border-left: 4px solid var(--info-color);
}

.toast-icon {
  font-size: 20px;
}

.toast-success .toast-icon {
  color: var(--success-color);
}

.toast-error .toast-icon {
  color: var(--error-color);
}

.toast-warning .toast-icon {
  color: var(--warning-color);
}

.toast-info .toast-icon {
  color: var(--info-color);
}

.toast-message {
  flex: 1;
  font-size: var(--font-size-sm);
  color: var(--text-primary);
}

.toast-close {
  background: none;
  border: none;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  cursor: pointer;
  color: var(--text-secondary);
  transition: all var(--transition-fast);
}

.toast-close:hover {
  background: var(--surface-variant);
  color: var(--text-primary);
}

/* Card Styles */
.card {
  background: var(--surface-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: all var(--transition-fast);
}

.card:hover {
  box-shadow: var(--shadow-lg);
}

.card-header {
  padding: var(--spacing-lg) var(--spacing-xl);
  border-bottom: 1px solid var(--border-color);
  background: var(--surface-variant);
}

.card-title {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.card-body {
  padding: var(--spacing-xl);
}

.card-footer {
  padding: var(--spacing-lg) var(--spacing-xl);
  border-top: 1px solid var(--border-color);
  background: var(--surface-variant);
  display: flex;
  gap: var(--spacing-md);
  justify-content: flex-end;
}

/* Loading Spinner Sizes */
.loading-spinner.size-small .spinner {
  width: 20px;
  height: 20px;
  border-width: 2px;
}

.loading-spinner.size-large .spinner {
  width: 60px;
  height: 60px;
  border-width: 6px;
}

/* Enhanced UX Features */
.filter-btn {
  background: none;
  border: 1px solid var(--border-color);
  padding: 4px 8px;
  font-size: var(--font-size-xs);
  cursor: pointer;
  border-radius: 2px;
  transition: all 0.2s ease;
}

.filter-btn:hover {
  background: var(--kipl-light-blue);
  border-color: var(--kipl-blue);
}

.filter-btn.active {
  background: var(--kipl-blue);
  color: white;
  border-color: var(--kipl-blue);
}

.quick-search {
  padding: 4px 8px;
  border: 1px solid var(--border-color);
  font-size: var(--font-size-xs);
  width: 150px;
  border-radius: 2px;
}

.quick-search:focus {
  outline: none;
  border-color: var(--kipl-blue);
}

.bulk-action-btn {
  background: var(--kipl-orange);
  color: white;
  border: none;
  padding: 4px 8px;
  font-size: var(--font-size-xs);
  cursor: pointer;
  border-radius: 2px;
}

.bulk-action-btn:hover {
  background: #E69500;
}

.detail-view {
  padding: 16px;
}

.detail-row {
  margin-bottom: 8px;
  padding: 4px 0;
  border-bottom: 1px solid var(--border-light);
}

.quick-edit-form {
  padding: 16px;
}

.form-row {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-row label {
  min-width: 100px;
  font-size: var(--font-size-sm);
  font-weight: bold;
}

.form-row select,
.form-row input {
  flex: 1;
  padding: 4px 8px;
  border: 1px solid var(--border-color);
  font-size: var(--font-size-sm);
  border-radius: 2px;
}

.form-row select:focus,
.form-row input:focus {
  outline: none;
  border-color: var(--kipl-blue);
}

/* Keyboard shortcuts hint */
.keyboard-hint {
  position: fixed;
  bottom: 16px;
  right: 16px;
  background: rgba(0,0,0,0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: var(--font-size-xs);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.keyboard-hint.show {
  opacity: 1;
}

/* Row selection */
.data-table tr.selected {
  background: rgba(68, 114, 196, 0.1) !important;
  border-left: 3px solid var(--kipl-blue);
}

/* Quick action tooltips */
.action-icon[title]:hover::after {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0,0,0,0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  white-space: nowrap;
  z-index: 1000;
}
