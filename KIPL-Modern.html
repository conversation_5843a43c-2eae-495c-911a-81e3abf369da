<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>KIPL Modern Warehouse Management</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <!-- Material Icons -->
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  
  <style>
    /* KIPL Original Colors */
    :root {
      --kipl-sidebar-blue: #2F5597;
      --kipl-header-orange: #FFA500;
      --kipl-table-header: #D9E2F3;
      --kipl-row-even: #F2F2F2;
      --kipl-row-odd: #FFFFFF;
      --kipl-border: #8EA9DB;
      --text-primary: #000000;
      --text-white: #FFFFFF;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      font-size: 12px;
      background-color: #F2F2F2;
      line-height: 1.3;
    }

    .app-container {
      display: flex;
      min-height: 100vh;
    }

    /* Sidebar */
    .sidebar {
      width: 200px;
      background: var(--kipl-sidebar-blue);
      position: fixed;
      height: 100vh;
      left: 0;
      top: 0;
      z-index: 1000;
      transition: all 0.3s ease;
    }

    .sidebar-header {
      padding: 10px;
      background: var(--kipl-sidebar-blue);
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 50px;
      border-bottom: 1px solid rgba(255,255,255,0.2);
    }

    .logo {
      color: var(--text-white);
      font-weight: bold;
      font-size: 14px;
    }

    .nav-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      text-decoration: none;
      color: var(--text-white);
      font-size: 12px;
      border-bottom: 1px solid rgba(255,255,255,0.1);
      transition: all 0.2s ease;
      cursor: pointer;
    }

    .nav-item:hover {
      background-color: rgba(255,255,255,0.1);
    }

    .nav-item.active {
      background: rgba(255,255,255,0.2);
      border-left: 4px solid var(--kipl-header-orange);
    }

    /* Main Content */
    .main-content {
      flex: 1;
      margin-left: 200px;
      display: flex;
      flex-direction: column;
    }

    /* Header */
    .app-header {
      background: var(--kipl-header-orange);
      border-bottom: 2px solid #E69500;
      padding: 8px 15px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      min-height: 45px;
    }

    .page-title {
      font-size: 16px;
      font-weight: bold;
      color: var(--text-white);
      margin: 0;
    }

    .user-profile {
      display: flex;
      align-items: center;
      gap: 6px;
      color: var(--text-white);
      font-size: 12px;
    }

    /* Content Area */
    .content-area {
      flex: 1;
      padding: 8px;
    }

    /* Modern Data Table Container */
    .modern-table-container {
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .table-header {
      padding: 12px 15px;
      background: linear-gradient(135deg, var(--kipl-table-header), #E8F1FF);
      border-bottom: 2px solid var(--kipl-border);
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .table-title {
      font-size: 14px;
      font-weight: bold;
      color: var(--text-primary);
      margin: 0;
    }

    .table-actions {
      display: flex;
      gap: 12px;
      align-items: center;
      flex-wrap: wrap;
    }

    .quick-filters {
      display: flex;
      gap: 4px;
      align-items: center;
      border: 1px solid var(--kipl-border);
      border-radius: 15px;
      padding: 2px;
      background: white;
    }

    .quick-actions {
      display: flex;
      gap: 4px;
      align-items: center;
    }

    .action-btn {
      background: white;
      border: 1px solid var(--kipl-border);
      padding: 6px;
      cursor: pointer;
      border-radius: 8px;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
    }

    .action-btn:hover {
      background: var(--kipl-table-header);
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .action-btn .material-icons {
      font-size: 16px;
      color: var(--kipl-sidebar-blue);
    }

    /* View Tab Structure */
    .view-tabs {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .view-tab-header {
      background: linear-gradient(135deg, #F0F4F8, #E2E8F0);
      padding: 12px 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 2px solid var(--kipl-border);
    }

    .view-tab-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: bold;
      font-size: 14px;
      color: var(--kipl-sidebar-blue);
    }

    .view-controls {
      display: flex;
      gap: 8px;
    }

    .view-btn, .close-btn {
      background: white;
      border: 1px solid var(--kipl-border);
      padding: 6px;
      cursor: pointer;
      border-radius: 4px;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 28px;
      height: 28px;
    }

    .view-btn:hover {
      background: var(--kipl-table-header);
    }

    .close-btn {
      background: #DC3545;
      border-color: #DC3545;
    }

    .close-btn .material-icons {
      color: white;
      font-size: 14px;
    }

    .view-btn .material-icons {
      font-size: 14px;
      color: var(--kipl-sidebar-blue);
    }

    /* Form Fields Section */
    .form-fields-section {
      padding: 16px;
      background: #FAFBFC;
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: 16px;
      margin-bottom: 12px;
    }

    .form-field {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .form-field.full-width {
      grid-column: 1 / -1;
    }

    .form-field label {
      font-size: 11px;
      font-weight: 600;
      color: #374151;
      margin-bottom: 2px;
    }

    .form-field input,
    .form-field select {
      padding: 6px 8px;
      border: 1px solid #D1D5DB;
      border-radius: 4px;
      font-size: 11px;
      background: white;
      transition: all 0.2s ease;
    }

    .form-field input:focus,
    .form-field select:focus {
      outline: none;
      border-color: var(--kipl-sidebar-blue);
      box-shadow: 0 0 0 2px rgba(47, 85, 151, 0.1);
    }

    .form-field input[readonly] {
      background: #F3F4F6;
      color: #6B7280;
    }

    /* Status Buttons */
    .status-buttons {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }

    .status-btn {
      padding: 6px 12px;
      border: none;
      border-radius: 4px;
      font-size: 10px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .status-btn.kitting-alternate {
      background: #FEF3C7;
      color: #92400E;
    }

    .status-btn.kitting-friendly {
      background: #DBEAFE;
      color: #1E40AF;
    }

    .status-btn.kitting-allocated {
      background: #E0E7FF;
      color: #3730A3;
    }

    .status-btn.kitting-progress {
      background: #FED7AA;
      color: #C2410C;
    }

    .status-btn.kitting-completed {
      background: #D1FAE5;
      color: #065F46;
    }

    .status-btn:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .filter-btn {
      background: white;
      border: 1px solid var(--kipl-border);
      padding: 4px 10px;
      font-size: 11px;
      cursor: pointer;
      border-radius: 15px;
      transition: all 0.2s ease;
      font-weight: 500;
    }

    .filter-btn:hover {
      background: var(--kipl-table-header);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .filter-btn.active {
      background: var(--kipl-sidebar-blue);
      color: white;
      border-color: var(--kipl-sidebar-blue);
      box-shadow: 0 4px 12px rgba(47, 85, 151, 0.3);
    }

    .quick-search {
      padding: 6px 10px;
      border: 1px solid var(--kipl-border);
      font-size: 11px;
      width: 180px;
      border-radius: 15px;
      transition: all 0.2s ease;
    }

    .quick-search:focus {
      outline: none;
      border-color: var(--kipl-sidebar-blue);
      box-shadow: 0 0 0 2px rgba(47, 85, 151, 0.1);
    }

    .add-new-btn {
      background: var(--kipl-header-orange);
      color: white;
      border: none;
      padding: 6px 12px;
      font-size: 11px;
      cursor: pointer;
      border-radius: 15px;
      display: flex;
      align-items: center;
      gap: 4px;
      transition: all 0.2s ease;
      font-weight: bold;
    }

    .add-new-btn:hover {
      background: #E69500;
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(255, 165, 0, 0.3);
    }

    /* Modern Search Filters */
    .modern-filters {
      background: linear-gradient(135deg, #F8F9FA, #FFFFFF);
      padding: 12px 15px;
      border-bottom: 1px solid var(--kipl-border);
    }

    .filters-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
    }

    .filters-title {
      display: flex;
      align-items: center;
      gap: 6px;
      font-weight: bold;
      font-size: 13px;
      color: var(--kipl-sidebar-blue);
    }

    .clear-all-btn {
      background: #DC3545;
      color: white;
      border: none;
      padding: 4px 8px;
      border-radius: 12px;
      cursor: pointer;
      font-size: 10px;
      display: flex;
      align-items: center;
      gap: 4px;
      font-weight: 500;
      transition: all 0.2s ease;
    }

    .clear-all-btn:hover {
      background: #C82333;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
    }

    .filters-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
      gap: 10px;
    }

    .search-group {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .search-label {
      font-size: 11px;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 2px;
    }

    .search-input-container {
      position: relative;
    }

    .modern-search-input {
      width: 100%;
      padding: 6px 8px 6px 28px;
      border: 1px solid #E1E5E9;
      border-radius: 12px;
      font-size: 11px;
      background: white;
      transition: all 0.2s ease;
      box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }

    .modern-search-input:focus {
      outline: none;
      border-color: var(--kipl-sidebar-blue);
      box-shadow: 0 0 0 2px rgba(47, 85, 151, 0.1);
    }

    .search-icon {
      position: absolute;
      left: 8px;
      top: 50%;
      transform: translateY(-50%);
      color: #6C757D;
      font-size: 14px;
      pointer-events: none;
    }

    /* Data Table */
    .table-wrapper {
      overflow-x: auto;
      min-height: 400px;
      background: white;
    }

    .data-table {
      width: 100%;
      min-width: 1200px;
      border-collapse: separate;
      border-spacing: 0;
      font-size: 11px;
    }

    .data-table th {
      background: var(--kipl-table-header);
      padding: 8px 6px;
      text-align: center;
      font-weight: bold;
      color: var(--text-primary);
      border: 1px solid var(--kipl-border);
      font-size: 11px;
      position: sticky;
      top: 0;
      z-index: 10;
      position: relative;
      user-select: none;
      min-width: 80px;
    }

    /* Proper Column Resizer */
    .column-resizer {
      position: absolute;
      top: 0;
      right: -2px;
      width: 4px;
      height: 100%;
      cursor: col-resize;
      background: transparent;
      z-index: 11;
      border-right: 2px solid transparent;
    }

    .column-resizer:hover {
      border-right: 2px solid var(--kipl-sidebar-blue);
    }

    .column-resizer.resizing {
      border-right: 2px solid var(--kipl-header-orange);
    }

    /* Resizable columns */
    .data-table th.resizable {
      overflow: hidden;
      white-space: nowrap;
    }

    .data-table th.resizable:hover {
      background: #C5D9F1;
    }

    .data-table td {
      padding: 6px 4px;
      border: 1px solid var(--kipl-border);
      color: var(--text-primary);
      font-size: 11px;
      text-align: center;
    }

    .data-table tr:nth-child(even) {
      background: var(--kipl-row-even);
    }

    .data-table tr:nth-child(odd) {
      background: var(--kipl-row-odd);
    }

    .data-table tr:hover {
      background: #E6F3FF !important;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      transition: all 0.2s ease;
    }

    /* Status Badges */
    .status-badge {
      display: inline-block;
      padding: 3px 8px;
      font-size: 10px;
      font-weight: 500;
      text-align: center;
      min-width: 80px;
      border-radius: 10px;
    }

    .status-kitting-completed {
      background: #28A745;
      color: white;
    }

    .status-in-progress {
      background: #007BFF;
      color: white;
    }

    .status-pending {
      background: #FFC107;
      color: black;
    }

    .status-quality-check {
      background: #6F42C1;
      color: white;
    }

    .status-on-hold {
      background: #FD7E14;
      color: white;
    }

    .status-cancelled {
      background: #DC3545;
      color: white;
    }

    /* Action Icons */
    .action-icons {
      display: flex;
      gap: 4px;
      justify-content: center;
    }

    .action-icon {
      font-size: 14px;
      cursor: pointer;
      color: var(--kipl-sidebar-blue);
      padding: 3px;
      border-radius: 50%;
      transition: all 0.2s ease;
    }

    .action-icon:hover {
      background: rgba(47, 85, 151, 0.1);
      transform: scale(1.2);
    }

    /* Pagination */
    .pagination {
      padding: 8px 15px;
      background: white;
      border-top: 1px solid var(--kipl-border);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .pagination-info {
      font-size: 11px;
      color: #666;
      font-weight: 500;
    }

    .pagination-controls {
      display: flex;
      gap: 4px;
    }

    .page-btn {
      background: white;
      border: 1px solid var(--kipl-border);
      padding: 4px 8px;
      cursor: pointer;
      font-size: 11px;
      border-radius: 4px;
      transition: all 0.2s ease;
      font-weight: 500;
    }

    .page-btn:hover {
      background: var(--kipl-table-header);
      transform: translateY(-1px);
    }

    .page-btn.active {
      background: var(--kipl-sidebar-blue);
      color: white;
      border-color: var(--kipl-sidebar-blue);
    }

    /* Toast Animations */
    @keyframes slideInRight {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }

    @keyframes slideOutRight {
      from {
        transform: translateX(0);
        opacity: 1;
      }
      to {
        transform: translateX(100%);
        opacity: 0;
      }
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .sidebar {
        width: 60px;
      }

      .main-content {
        margin-left: 60px;
      }

      .filters-grid {
        grid-template-columns: 1fr;
      }

      .table-actions {
        flex-direction: column;
        align-items: stretch;
      }

      .quick-search {
        width: 100%;
      }

      .quick-filters, .quick-actions {
        justify-content: center;
      }
    }
  </style>
</head>
<body>
  <div class="app-container">
    <!-- Sidebar -->
    <aside class="sidebar">
      <div class="sidebar-header">
        <div class="logo">KOMATSU</div>
      </div>
      
      <nav class="sidebar-nav">
        <div class="nav-item">
          <span class="material-icons">home</span>
          <span>Home</span>
        </div>
        <div class="nav-item active">
          <span class="material-icons">assignment</span>
          <span>Production Plan</span>
        </div>
        <div class="nav-item">
          <span class="material-icons">list_alt</span>
          <span>Firm Order</span>
        </div>
        <div class="nav-item">
          <span class="material-icons">local_shipping</span>
          <span>Logistics</span>
        </div>
        <div class="nav-item">
          <span class="material-icons">build</span>
          <span>Kitting</span>
        </div>
        <div class="nav-item">
          <span class="material-icons">analytics</span>
          <span>Reports</span>
        </div>
        <div class="nav-item">
          <span class="material-icons">settings</span>
          <span>Settings</span>
        </div>
      </nav>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
      <!-- Header -->
      <header class="app-header">
        <h1 class="page-title">Production Plan</h1>
        <div class="user-profile">
          <span class="material-icons">account_circle</span>
          <div>Admin User</div>
        </div>
      </header>

      <!-- Content Area -->
      <div class="content-area">
        <div class="modern-table-container">
          <!-- View Tab Structure -->
          <div class="view-tabs">
            <div class="view-tab-header">
              <div class="view-tab-title">
                <span class="material-icons">view_list</span>
                Production Plan
              </div>
              <div class="view-controls">
                <button class="view-btn" onclick="exportData()" title="Export">
                  <span class="material-icons">download</span>
                </button>
                <button class="view-btn" onclick="refreshData()" title="Refresh">
                  <span class="material-icons">refresh</span>
                </button>
                <button class="close-btn" onclick="closeView()" title="Close">
                  <span class="material-icons">close</span>
                </button>
              </div>
            </div>

            <!-- Form Fields Section -->
            <div class="form-fields-section">
              <div class="form-row">
                <div class="form-field">
                  <label>Plan Id</label>
                  <input type="text" value="105" readonly>
                </div>
                <div class="form-field">
                  <label>Market</label>
                  <select>
                    <option>Export</option>
                    <option>Domestic</option>
                  </select>
                </div>
                <div class="form-field">
                  <label>Prod Month</label>
                  <input type="text" value="May 25" readonly>
                </div>
              </div>

              <div class="form-row">
                <div class="form-field">
                  <label>Model</label>
                  <input type="text" value="PC210-NI@-10M0" readonly>
                </div>
                <div class="form-field">
                  <label>Machine #</label>
                  <input type="text" value="N107" readonly>
                </div>
                <div class="form-field">
                  <label>SPEC Pattern</label>
                  <input type="text" value="NCRE" readonly>
                </div>
              </div>

              <div class="form-row">
                <div class="form-field">
                  <label>Order Number</label>
                  <input type="text" value="SARASANELK" readonly>
                </div>
                <div class="form-field">
                  <label>Arm</label>
                  <input type="text" value="2.5M+RB" readonly>
                </div>
                <div class="form-field">
                  <label>Bucket</label>
                  <input type="text" value="BUCKET LESS" readonly>
                </div>
              </div>

              <div class="form-row">
                <div class="form-field">
                  <label>Mesh Guard</label>
                  <input type="text" value="" placeholder="Enter mesh guard">
                </div>
                <div class="form-field">
                  <label>Add Lamp</label>
                  <input type="text" value="" placeholder="Enter add lamp">
                </div>
                <div class="form-field">
                  <label>Mesh Guard/Add Lamp</label>
                  <input type="text" value="" placeholder="Enter details">
                </div>
              </div>

              <div class="form-row">
                <div class="form-field">
                  <label>Top Guard</label>
                  <input type="text" value="" placeholder="Enter top guard">
                </div>
                <div class="form-field">
                  <label>Beacon Lamp</label>
                  <input type="text" value="" placeholder="Enter beacon lamp">
                </div>
                <div class="form-field">
                  <label>Top Guard/Add Lamp</label>
                  <input type="text" value="" placeholder="Enter details">
                </div>
              </div>

              <div class="form-row">
                <div class="form-field">
                  <label>Mesh Guard/Top Guard/Add Lamp</label>
                  <input type="text" value="" placeholder="Enter details">
                </div>
                <div class="form-field">
                  <label>Shortage Status</label>
                  <input type="text" value="" placeholder="Enter status">
                </div>
                <div class="form-field">
                  <label>Production Start Date</label>
                  <input type="date" value="2025-04-29">
                </div>
              </div>

              <div class="form-row">
                <div class="form-field">
                  <label>Is Shortage</label>
                  <input type="text" value="" placeholder="Yes/No">
                </div>
                <div class="form-field">
                  <label>Is Correction</label>
                  <input type="text" value="" placeholder="Yes/No">
                </div>
                <div class="form-field">
                  <label>Correction Status</label>
                  <select>
                    <option>In Progress</option>
                    <option>Completed</option>
                    <option>Pending</option>
                  </select>
                </div>
              </div>

              <div class="form-row">
                <div class="form-field full-width">
                  <label>Production Status</label>
                  <div class="status-buttons">
                    <button class="status-btn kitting-alternate">Kitting for Kitting Alternate</button>
                    <button class="status-btn kitting-friendly">Kitting Friendly Allocated</button>
                    <button class="status-btn kitting-allocated">Kitting Allocated</button>
                    <button class="status-btn kitting-progress">Kitting In Progress</button>
                    <button class="status-btn kitting-completed">Kitting Completed</button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="modern-filters">
            <div class="filters-header">
              <div class="filters-title">
                <span class="material-icons">search</span>
                <span>Smart Search & Filters</span>
              </div>
              <button class="clear-all-btn" onclick="clearAllFilters()">
                <span class="material-icons">clear_all</span>
                Clear All
              </button>
            </div>

            <div class="filters-grid">
              <div class="search-group">
                <label class="search-label">Status</label>
                <div class="search-input-container">
                  <span class="material-icons search-icon">search</span>
                  <input type="text" class="modern-search-input" placeholder="Search status..." onkeyup="filterColumn('status', this.value)">
                </div>
              </div>

              <div class="search-group">
                <label class="search-label">Market</label>
                <div class="search-input-container">
                  <span class="material-icons search-icon">public</span>
                  <input type="text" class="modern-search-input" placeholder="Search market..." onkeyup="filterColumn('market', this.value)">
                </div>
              </div>

              <div class="search-group">
                <label class="search-label">Model</label>
                <div class="search-input-container">
                  <span class="material-icons search-icon">precision_manufacturing</span>
                  <input type="text" class="modern-search-input" placeholder="Search model..." onkeyup="filterColumn('model', this.value)">
                </div>
              </div>

              <div class="search-group">
                <label class="search-label">Machine #</label>
                <div class="search-input-container">
                  <span class="material-icons search-icon">tag</span>
                  <input type="text" class="modern-search-input" placeholder="Search machine..." onkeyup="filterColumn('machineNumber', this.value)">
                </div>
              </div>

              <div class="search-group">
                <label class="search-label">Order #</label>
                <div class="search-input-container">
                  <span class="material-icons search-icon">receipt_long</span>
                  <input type="text" class="modern-search-input" placeholder="Search order..." onkeyup="filterColumn('orderNumber', this.value)">
                </div>
              </div>

              <div class="search-group">
                <label class="search-label">Cabin</label>
                <div class="search-input-container">
                  <span class="material-icons search-icon">airline_seat_recline_normal</span>
                  <input type="text" class="modern-search-input" placeholder="Search cabin..." onkeyup="filterColumn('cabin', this.value)">
                </div>
              </div>

              <div class="search-group">
                <label class="search-label">DGMS</label>
                <div class="search-input-container">
                  <span class="material-icons search-icon">settings</span>
                  <input type="text" class="modern-search-input" placeholder="Search DGMS..." onkeyup="filterColumn('dgms', this.value)">
                </div>
              </div>

              <div class="search-group">
                <label class="search-label">Revision #</label>
                <div class="search-input-container">
                  <span class="material-icons search-icon">history</span>
                  <input type="text" class="modern-search-input" placeholder="Search revision..." onkeyup="filterColumn('revisionNo', this.value)">
                </div>
              </div>
            </div>
          </div>

          <div class="table-wrapper">
            <table class="data-table" id="productionTable">
              <thead>
                <tr>
                  <th class="resizable" data-column="actions">
                    Actions
                    <div class="column-resizer"></div>
                  </th>
                  <th class="resizable" data-column="status">
                    Status
                    <div class="column-resizer"></div>
                  </th>
                  <th class="resizable" data-column="revision">
                    Revision #
                    <div class="column-resizer"></div>
                  </th>
                  <th class="resizable" data-column="market">
                    Market
                    <div class="column-resizer"></div>
                  </th>
                  <th class="resizable" data-column="prodMonth">
                    Prod Month
                    <div class="column-resizer"></div>
                  </th>
                  <th class="resizable" data-column="model">
                    Model
                    <div class="column-resizer"></div>
                  </th>
                  <th class="resizable" data-column="machine">
                    Machine #
                    <div class="column-resizer"></div>
                  </th>
                  <th class="resizable" data-column="spec">
                    SPEC Pattern
                    <div class="column-resizer"></div>
                  </th>
                  <th class="resizable" data-column="order">
                    Order #
                    <div class="column-resizer"></div>
                  </th>
                  <th class="resizable" data-column="cabin">
                    Cabin
                    <div class="column-resizer"></div>
                  </th>
                  <th class="resizable" data-column="arm">
                    Arm
                    <div class="column-resizer"></div>
                  </th>
                  <th class="resizable" data-column="bucket">
                    Bucket
                    <div class="column-resizer"></div>
                  </th>
                  <th class="resizable" data-column="dgms">
                    DGMS
                    <div class="column-resizer"></div>
                  </th>
                  <th class="resizable" data-column="meshGuard">
                    Mesh Guard / Add
                    <div class="column-resizer"></div>
                  </th>
                  <th class="resizable" data-column="productionStart">
                    Production Start Date
                    <div class="column-resizer"></div>
                  </th>
                </tr>
              </thead>
              <tbody id="tableBody">
                <!-- Data will be loaded here -->
              </tbody>
            </table>
          </div>

          <div class="pagination">
            <div class="pagination-info">Showing 1-10 of 50 entries</div>
            <div class="pagination-controls">
              <button class="page-btn active" onclick="goToPage(1)">1</button>
              <button class="page-btn" onclick="goToPage(2)">2</button>
              <button class="page-btn" onclick="goToPage(3)">3</button>
              <button class="page-btn" onclick="goToPage(4)">4</button>
              <button class="page-btn" onclick="goToPage(5)">5</button>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>

  <script>
    // Sample data with proper DGMS values
    const productionData = [
      {
        id: 1,
        status: 'Kitting Completed',
        revisionNo: 1,
        market: 'DOMESTIC',
        prodMonth: 'Sep 19',
        model: 'PC130-NI@-7',
        machineNumber: 'N521950',
        specPattern: 'NAC',
        orderNumber: 'S86NSS64D',
        cabin: 'NON-AC',
        arm: '2.1M',
        bucket: '0.64_CUM_NON-DGMS',
        dgms: 'NON-DGMS',
        meshGuard: 'Mesh Guard',
        productionStart: '28/08/2019'
      },
      {
        id: 2,
        status: 'In Progress',
        revisionNo: 2,
        market: 'DOMESTIC',
        prodMonth: 'Sep 19',
        model: 'PC130-NI@-7',
        machineNumber: 'N521951',
        specPattern: 'NCA',
        orderNumber: 'S86NSS64D',
        cabin: 'NON-AC',
        arm: '2.1M TATT',
        bucket: '0.64_CUM_NON-DGMS',
        dgms: 'DGMS',
        meshGuard: 'Mesh Guard',
        productionStart: '29/08/2019'
      },
      {
        id: 3,
        status: 'Pending',
        revisionNo: 1,
        market: 'DOMESTIC',
        prodMonth: 'Sep 19',
        model: 'PC130-NI@-7',
        machineNumber: 'N521952',
        specPattern: 'NAC',
        orderNumber: 'S86NSS64D',
        cabin: 'NON-AC',
        arm: '2.1M TATT',
        bucket: '0.64_CUM_NON-DGMS',
        dgms: 'NON-DGMS',
        meshGuard: 'Mesh Guard',
        productionStart: '30/08/2019'
      },
      {
        id: 4,
        status: 'Quality Check',
        revisionNo: 3,
        market: 'DOMESTIC',
        prodMonth: 'Sep 19',
        model: 'PC130-NI@-7',
        machineNumber: 'N521953',
        specPattern: 'NAC',
        orderNumber: 'S86NSS64D',
        cabin: 'NON-AC',
        arm: '2.1M TATT',
        bucket: '0.64_CUM_NON-DGMS',
        dgms: 'DGMS',
        meshGuard: 'Mesh Guard',
        productionStart: '31/08/2019'
      },
      {
        id: 5,
        status: 'Kitting Completed',
        revisionNo: 4,
        market: 'DOMESTIC',
        prodMonth: 'Sep 19',
        model: 'PC130-NI@-7',
        machineNumber: 'N521954',
        specPattern: 'NAC',
        orderNumber: 'S86NSS64D',
        cabin: 'NON-AC',
        arm: '2.1M TATT',
        bucket: '0.64_CUM_NON-DGMS',
        dgms: 'NON-DGMS',
        meshGuard: 'Mesh Guard',
        productionStart: '01/09/2019'
      }
    ];

    // Generate additional data to reach 50 records
    for (let i = 6; i <= 50; i++) {
      const statuses = ['Kitting Completed', 'In Progress', 'Pending', 'Quality Check', 'On Hold', 'Cancelled'];
      const markets = ['DOMESTIC', 'EXPORT'];
      const models = ['PC130-NI@-7', 'PC210-NI@-8MC', 'PC210-NI@-10M0', 'PC300-8M0', 'PC400-7', 'PC450-8'];
      const cabins = ['AC', 'NON-AC'];
      const dgmsValues = ['DGMS', 'NON-DGMS'];

      productionData.push({
        id: i,
        status: statuses[Math.floor(Math.random() * statuses.length)],
        revisionNo: Math.floor(Math.random() * 5) + 1,
        market: markets[Math.floor(Math.random() * markets.length)],
        prodMonth: ['Sep 19', 'Oct 19', 'Nov 19', 'Dec 19', 'Jan 20'][Math.floor(Math.random() * 5)],
        model: models[Math.floor(Math.random() * models.length)],
        machineNumber: `N${Math.floor(Math.random() * 900000) + 100000}`,
        specPattern: ['NAC', 'NCA', 'NAA'][Math.floor(Math.random() * 3)],
        orderNumber: `${markets[Math.floor(Math.random() * 2)] === 'DOMESTIC' ? 'S' : 'E'}${Math.floor(Math.random() * 90) + 10}ABC${Math.floor(Math.random() * 900) + 100}`,
        cabin: cabins[Math.floor(Math.random() * cabins.length)],
        arm: ['2.1M', '2.5M', '2.8M', '3.0M', '3.2M'][Math.floor(Math.random() * 5)] + (Math.random() > 0.5 ? ' TATT' : ''),
        bucket: ['0.64_CUM_NON-DGMS', '0.8_CUM_STD', '1.0_CUM_NON-DGMS', '1.5_CUM_XL'][Math.floor(Math.random() * 4)],
        dgms: dgmsValues[Math.floor(Math.random() * dgmsValues.length)],
        meshGuard: ['Mesh Guard', 'Mesh Guard Ext', 'Mesh Guard Ext Ls'][Math.floor(Math.random() * 3)],
        productionStart: `${Math.floor(Math.random() * 28) + 1}/${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}/2019`
      });
    }

    let currentData = productionData;
    let currentPage = 1;
    const itemsPerPage = 10;
    let activeFilters = {};

    function createStatusBadge(status) {
      const statusClass = status.toLowerCase().replace(/\s+/g, '-');
      return `<span class="status-badge status-${statusClass}">${status}</span>`;
    }

    function createActionIcons(row) {
      return `
        <div class="action-icons">
          <span class="material-icons action-icon" title="View Production Details" onclick="viewDetails(${row.id})">visibility</span>
          <span class="material-icons action-icon" title="Edit Production Plan" onclick="editRecord(${row.id})">edit</span>
          <span class="material-icons action-icon" title="Copy Production Plan" onclick="copyRecord(${row.id})">content_copy</span>
          <span class="material-icons action-icon" title="Delete Production Plan" onclick="deleteRecord(${row.id})">delete</span>
        </div>
      `;
    }

    function loadTable() {
      const tbody = document.getElementById('tableBody');
      const startIndex = (currentPage - 1) * itemsPerPage;
      const endIndex = startIndex + itemsPerPage;
      const pageData = currentData.slice(startIndex, endIndex);

      if (pageData.length === 0) {
        tbody.innerHTML = `
          <tr>
            <td colspan="15" style="text-align: center; padding: 40px; color: #666; font-size: 16px;">
              <div style="display: flex; flex-direction: column; align-items: center; gap: 10px;">
                <span class="material-icons" style="font-size: 48px; color: #ccc;">search_off</span>
                <div>No records found matching your search criteria</div>
                <button onclick="clearAllFilters()" style="background: var(--kipl-sidebar-blue); color: white; border: none; padding: 8px 16px; border-radius: 20px; cursor: pointer;">Clear Filters</button>
              </div>
            </td>
          </tr>
        `;
      } else {
        tbody.innerHTML = pageData.map(row => `
          <tr>
            <td>${createActionIcons(row)}</td>
            <td>${createStatusBadge(row.status)}</td>
            <td><strong>${row.revisionNo}</strong></td>
            <td><span style="background: ${row.market === 'DOMESTIC' ? '#E3F2FD' : '#FFF3E0'}; padding: 2px 6px; border-radius: 8px; font-size: 10px; font-weight: 500;">${row.market}</span></td>
            <td>${row.prodMonth}</td>
            <td><strong>${row.model}</strong></td>
            <td><code style="background: #f8f9fa; padding: 1px 4px; border-radius: 3px; font-size: 10px;">${row.machineNumber}</code></td>
            <td>${row.specPattern}</td>
            <td><code style="background: #f8f9fa; padding: 1px 4px; border-radius: 3px; font-size: 10px;">${row.orderNumber}</code></td>
            <td><span style="background: ${row.cabin === 'AC' ? '#E8F5E8' : '#FFF3E0'}; padding: 2px 6px; border-radius: 8px; font-size: 10px; font-weight: 500;">${row.cabin}</span></td>
            <td>${row.arm}</td>
            <td>${row.bucket}</td>
            <td><span style="background: ${row.dgms === 'DGMS' ? '#E3F2FD' : '#FFEBEE'}; padding: 2px 6px; border-radius: 8px; font-size: 10px; font-weight: 500; color: ${row.dgms === 'DGMS' ? '#1976D2' : '#D32F2F'};">${row.dgms}</span></td>
            <td>${row.meshGuard}</td>
            <td>${row.productionStart}</td>
          </tr>
        `).join('');
      }

      updatePagination();
    }

    function updatePagination() {
      const totalPages = Math.ceil(currentData.length / itemsPerPage);
      const startItem = ((currentPage - 1) * itemsPerPage) + 1;
      const endItem = Math.min(currentPage * itemsPerPage, currentData.length);

      document.querySelector('.pagination-info').textContent =
        `Showing ${startItem}-${endItem} of ${currentData.length} entries`;

      const controls = document.querySelector('.pagination-controls');
      controls.innerHTML = '';

      for (let i = 1; i <= Math.min(5, totalPages); i++) {
        const btn = document.createElement('button');
        btn.className = `page-btn ${i === currentPage ? 'active' : ''}`;
        btn.textContent = i;
        btn.onclick = () => goToPage(i);
        controls.appendChild(btn);
      }
    }

    function goToPage(page) {
      currentPage = page;
      loadTable();
    }

    function filterByStatus(status) {
      // Update active button
      document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
      event.target.classList.add('active');

      if (status === 'All') {
        delete activeFilters.status;
      } else {
        activeFilters.status = status.toLowerCase();
      }

      applyFilters();
    }

    function filterColumn(column, value) {
      if (value.trim() === '') {
        delete activeFilters[column];
      } else {
        activeFilters[column] = value.toLowerCase();
      }
      applyFilters();
    }

    function globalSearch(query) {
      if (query.trim() === '') {
        delete activeFilters.global;
      } else {
        activeFilters.global = query.toLowerCase();
      }
      applyFilters();
    }

    function applyFilters() {
      currentData = productionData.filter(row => {
        // Apply all filters
        for (let [key, value] of Object.entries(activeFilters)) {
          if (key === 'global') {
            // Global search across all fields
            const found = Object.values(row).some(field =>
              field.toString().toLowerCase().includes(value)
            );
            if (!found) return false;
          } else if (key === 'status') {
            if (!row.status.toLowerCase().includes(value)) return false;
          } else {
            const fieldValue = (row[key] || '').toString().toLowerCase();
            if (!fieldValue.includes(value)) return false;
          }
        }
        return true;
      });

      currentPage = 1;
      loadTable();
    }

    function clearAllFilters() {
      activeFilters = {};

      // Clear all search inputs
      document.querySelectorAll('.modern-search-input').forEach(input => {
        input.value = '';
      });

      // Clear global search
      document.querySelector('.quick-search').value = '';

      // Reset status filter
      document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
      document.querySelector('.filter-btn').classList.add('active');

      currentData = productionData;
      currentPage = 1;
      loadTable();
    }

    // Action functions
    function viewDetails(id) {
      alert(`Viewing details for production plan ID: ${id}`);
    }

    function editRecord(id) {
      alert(`Editing production plan ID: ${id}`);
    }

    function copyRecord(id) {
      alert(`Copying production plan ID: ${id}`);
    }

    function deleteRecord(id) {
      if (confirm(`Are you sure you want to delete production plan ID: ${id}?`)) {
        alert(`Production plan ID: ${id} has been deleted`);
      }
    }

    function addNewRecord() {
      alert('Add New Production Plan functionality - Modal would open here');
    }

    function closeView() {
      const viewTab = document.querySelector('.view-tabs');
      const tableContainer = document.querySelector('.table-wrapper').parentElement;

      if (viewTab.style.display === 'none') {
        viewTab.style.display = 'block';
        tableContainer.style.display = 'none';
        showToast('Detail view opened', 'info');
      } else {
        viewTab.style.display = 'none';
        tableContainer.style.display = 'block';
        showToast('Table view opened', 'info');
      }
    }

    // Improved Column Resizing Functionality
    let isResizing = false;
    let currentColumn = null;
    let startX = 0;
    let startWidth = 0;

    function initializeColumnResizing() {
      const table = document.querySelector('.data-table');
      if (!table) return;

      const resizers = document.querySelectorAll('.column-resizer');

      resizers.forEach((resizer, index) => {
        resizer.addEventListener('mousedown', (e) => {
          e.preventDefault();
          e.stopPropagation();

          isResizing = true;
          currentColumn = resizer.parentElement;
          startX = e.pageX;
          startWidth = currentColumn.offsetWidth;

          resizer.classList.add('resizing');
          document.body.style.cursor = 'col-resize';
          document.body.style.userSelect = 'none';

          // Add overlay to prevent text selection
          const overlay = document.createElement('div');
          overlay.id = 'resize-overlay';
          overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            cursor: col-resize;
            z-index: 9999;
          `;
          document.body.appendChild(overlay);
        });
      });

      document.addEventListener('mousemove', (e) => {
        if (!isResizing || !currentColumn) return;

        const diff = e.pageX - startX;
        const newWidth = startWidth + diff;

        if (newWidth >= 60) { // Minimum column width
          currentColumn.style.width = newWidth + 'px';
          currentColumn.style.minWidth = newWidth + 'px';
        }
      });

      document.addEventListener('mouseup', () => {
        if (isResizing) {
          isResizing = false;

          const resizer = document.querySelector('.column-resizer.resizing');
          if (resizer) {
            resizer.classList.remove('resizing');
          }

          document.body.style.cursor = '';
          document.body.style.userSelect = '';

          const overlay = document.getElementById('resize-overlay');
          if (overlay) {
            overlay.remove();
          }

          currentColumn = null;
        }
      });
    }

    // Toolbar Functions
    function exportData() {
      // Create CSV content
      const headers = ['Actions', 'Status', 'Revision #', 'Market', 'Prod Month', 'Model', 'Machine #', 'SPEC Pattern', 'Order #', 'Cabin', 'Arm', 'Bucket', 'DGMS', 'Mesh Guard / Add', 'Production Start Date'];
      const csvContent = [
        headers.join(','),
        ...currentData.map(row => [
          'View/Edit/Copy/Delete',
          row.status,
          row.revisionNo,
          row.market,
          row.prodMonth,
          row.model,
          row.machineNumber,
          row.specPattern,
          row.orderNumber,
          row.cabin,
          row.arm,
          row.bucket,
          row.dgms,
          row.meshGuard,
          row.productionStart
        ].join(','))
      ].join('\n');

      // Download CSV
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'production_plan.csv';
      a.click();
      window.URL.revokeObjectURL(url);

      showToast('Data exported successfully!', 'success');
    }

    function refreshData() {
      // Simulate data refresh
      showToast('Data refreshed!', 'info');
      loadTable();
    }

    function toggleFilters() {
      const filtersSection = document.querySelector('.modern-filters');
      if (filtersSection.style.display === 'none') {
        filtersSection.style.display = 'block';
        showToast('Search filters shown', 'info');
      } else {
        filtersSection.style.display = 'none';
        showToast('Search filters hidden', 'info');
      }
    }

    let compactView = false;
    function toggleView() {
      compactView = !compactView;
      const table = document.querySelector('.data-table');

      if (compactView) {
        table.style.fontSize = '10px';
        document.querySelectorAll('.data-table th, .data-table td').forEach(cell => {
          cell.style.padding = '4px 3px';
        });
        showToast('Compact view enabled', 'info');
      } else {
        table.style.fontSize = '11px';
        document.querySelectorAll('.data-table th, .data-table td').forEach(cell => {
          cell.style.padding = '6px 4px';
        });
        showToast('Normal view enabled', 'info');
      }
    }

    function showToast(message, type = 'info') {
      const toast = document.createElement('div');
      toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10001;
        font-size: 13px;
        font-weight: 500;
        animation: slideInRight 0.3s ease;
      `;
      toast.textContent = message;

      document.body.appendChild(toast);

      setTimeout(() => {
        toast.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
          if (toast.parentNode) {
            document.body.removeChild(toast);
          }
        }, 300);
      }, 3000);
    }

    // Initialize
    document.addEventListener('DOMContentLoaded', () => {
      console.log('Loading KIPL Modern with', productionData.length, 'records');
      loadTable();
      initializeColumnResizing();
    });
  </script>
</body>
</html>
