/**
 * API Service Layer for KIPL Warehouse Management System
 * Headless architecture - handles all backend communication
 */

class APIService {
  constructor() {
    this.baseURL = 'http://localhost:3000/api'; // Configure your backend URL
    this.headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };
    this.token = localStorage.getItem('auth_token');
    
    if (this.token) {
      this.headers['Authorization'] = `Bearer ${this.token}`;
    }
  }

  // Authentication methods
  async login(credentials) {
    try {
      const response = await this.request('POST', '/auth/login', credentials);
      if (response.token) {
        this.setAuthToken(response.token);
      }
      return response;
    } catch (error) {
      throw new Error(`Login failed: ${error.message}`);
    }
  }

  async logout() {
    try {
      await this.request('POST', '/auth/logout');
      this.clearAuthToken();
    } catch (error) {
      console.error('Logout error:', error);
    }
  }

  setAuthToken(token) {
    this.token = token;
    this.headers['Authorization'] = `Bearer ${token}`;
    localStorage.setItem('auth_token', token);
  }

  clearAuthToken() {
    this.token = null;
    delete this.headers['Authorization'];
    localStorage.removeItem('auth_token');
  }

  // Generic request method
  async request(method, endpoint, data = null) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      method,
      headers: { ...this.headers }
    };

    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      config.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        if (response.status === 401) {
          this.clearAuthToken();
          window.location.href = '/login';
          return;
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        return await response.json();
      }
      
      return await response.text();
    } catch (error) {
      console.error('API Request failed:', error);
      throw error;
    }
  }

  // Production Plan API methods
  async getProductionPlans(filters = {}) {
    const queryParams = new URLSearchParams(filters).toString();
    const endpoint = queryParams ? `/production-plans?${queryParams}` : '/production-plans';
    return await this.request('GET', endpoint);
  }

  async getProductionPlan(id) {
    return await this.request('GET', `/production-plans/${id}`);
  }

  async createProductionPlan(planData) {
    return await this.request('POST', '/production-plans', planData);
  }

  async updateProductionPlan(id, planData) {
    return await this.request('PUT', `/production-plans/${id}`, planData);
  }

  async deleteProductionPlan(id) {
    return await this.request('DELETE', `/production-plans/${id}`);
  }

  // Firm Order API methods
  async getFirmOrders(filters = {}) {
    const queryParams = new URLSearchParams(filters).toString();
    const endpoint = queryParams ? `/firm-orders?${queryParams}` : '/firm-orders';
    return await this.request('GET', endpoint);
  }

  async getFirmOrder(id) {
    return await this.request('GET', `/firm-orders/${id}`);
  }

  async createFirmOrder(orderData) {
    return await this.request('POST', '/firm-orders', orderData);
  }

  async updateFirmOrder(id, orderData) {
    return await this.request('PUT', `/firm-orders/${id}`, orderData);
  }

  // Logistics API methods
  async getLogistics(filters = {}) {
    const queryParams = new URLSearchParams(filters).toString();
    const endpoint = queryParams ? `/logistics?${queryParams}` : '/logistics';
    return await this.request('GET', endpoint);
  }

  async updateLogisticsStatus(id, status) {
    return await this.request('PATCH', `/logistics/${id}/status`, { status });
  }

  // Manufacturing API methods
  async getManufacturingData(filters = {}) {
    const queryParams = new URLSearchParams(filters).toString();
    const endpoint = queryParams ? `/manufacturing?${queryParams}` : '/manufacturing';
    return await this.request('GET', endpoint);
  }

  // Warehouse API methods
  async getWarehouseData(filters = {}) {
    const queryParams = new URLSearchParams(filters).toString();
    const endpoint = queryParams ? `/warehouse?${queryParams}` : '/warehouse';
    return await this.request('GET', endpoint);
  }

  async updateInventory(itemId, quantity) {
    return await this.request('PATCH', `/warehouse/inventory/${itemId}`, { quantity });
  }

  // Reports API methods
  async getReports(type, filters = {}) {
    const queryParams = new URLSearchParams(filters).toString();
    const endpoint = queryParams ? `/reports/${type}?${queryParams}` : `/reports/${type}`;
    return await this.request('GET', endpoint);
  }

  async generateReport(reportConfig) {
    return await this.request('POST', '/reports/generate', reportConfig);
  }

  // Dashboard API methods
  async getDashboardData() {
    return await this.request('GET', '/dashboard');
  }

  async getKPIs() {
    return await this.request('GET', '/dashboard/kpis');
  }

  // Settings API methods
  async getSettings() {
    return await this.request('GET', '/settings');
  }

  async updateSettings(settings) {
    return await this.request('PUT', '/settings', settings);
  }

  // Search API methods
  async search(query, filters = {}) {
    const searchData = { query, ...filters };
    return await this.request('POST', '/search', searchData);
  }

  // File upload method
  async uploadFile(file, endpoint = '/upload') {
    const formData = new FormData();
    formData.append('file', file);

    const config = {
      method: 'POST',
      headers: {
        'Authorization': this.headers['Authorization']
      },
      body: formData
    };

    try {
      const response = await fetch(`${this.baseURL}${endpoint}`, config);
      if (!response.ok) {
        throw new Error(`Upload failed: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('File upload failed:', error);
      throw error;
    }
  }

  // Utility methods
  isAuthenticated() {
    return !!this.token;
  }

  getAuthToken() {
    return this.token;
  }
}

// Create global API instance
window.api = new APIService();

// Mock data matching original KIPL table structure
window.mockData = {
  productionPlans: [
    {
      id: 1,
      slNo: 1,
      status: 'Kitting Completed',
      revisionNo: 1,
      market: 'DOMESTIC',
      prodMonth: 'Sep 19',
      model: 'PC130-NI@-7',
      machineNumber: 'N521950',
      specPattern: 'NAC',
      orderNumber: 'S86NSS64D',
      cabin: 'NON-AC',
      arm: '2.1M',
      bucket: '0.64_CUM_NON-DGMS',
      dgms: '',
      meshGuard: 'Mesh Guard',
      productionStart: '28/08/2019',
      priority: 'high'
    },
    {
      id: 2,
      slNo: 2,
      status: 'In Progress',
      revisionNo: 2,
      market: 'DOMESTIC',
      prodMonth: 'Sep 19',
      model: 'PC130-NI@-7',
      machineNumber: 'N521951',
      specPattern: 'NCA',
      orderNumber: 'S86NSS64D',
      cabin: 'NON-AC',
      arm: '2.1M TATT',
      bucket: '0.64_CUM_NON-DGMS',
      dgms: '',
      meshGuard: 'Mesh Guard',
      productionStart: '29/08/2019',
      priority: 'medium'
    },
    {
      id: 3,
      slNo: 3,
      status: 'Pending',
      revisionNo: 1,
      market: 'DOMESTIC',
      prodMonth: 'Sep 19',
      model: 'PC130-NI@-7',
      machineNumber: 'N521952',
      specPattern: 'NAC',
      orderNumber: 'S86NSS64D',
      cabin: 'NON-AC',
      arm: '2.1M TATT',
      bucket: '0.64_CUM_NON-DGMS',
      dgms: '',
      meshGuard: 'Mesh Guard',
      productionStart: '30/08/2019',
      priority: 'medium'
    },
    {
      id: 4,
      slNo: 4,
      status: 'Quality Check',
      revisionNo: 3,
      market: 'DOMESTIC',
      prodMonth: 'Sep 19',
      model: 'PC130-NI@-7',
      machineNumber: 'N521953',
      specPattern: 'NAC',
      orderNumber: 'S86NSS64D',
      cabin: 'NON-AC',
      arm: '2.1M TATT',
      bucket: '0.64_CUM_NON-DGMS',
      dgms: '',
      meshGuard: 'Mesh Guard',
      productionStart: '31/08/2019',
      priority: 'medium'
    },
    {
      id: 5,
      slNo: 5,
      status: 'Kitting Completed',
      revisionNo: 4,
      market: 'DOMESTIC',
      prodMonth: 'Sep 19',
      model: 'PC130-NI@-7',
      machineNumber: 'N521954',
      specPattern: 'NAC',
      orderNumber: 'S86NSS64D',
      cabin: 'NON-AC',
      arm: '2.1M TATT',
      bucket: '0.64_CUM_NON-DGMS',
      dgms: '',
      meshGuard: 'Mesh Guard',
      productionStart: '01/09/2019',
      priority: 'medium'
    },
    {
      id: 6,
      slNo: 6,
      status: 'On Hold',
      revisionNo: 2,
      market: 'DOMESTIC',
      prodMonth: 'Sep 19',
      model: 'PC130-NI@-7',
      machineNumber: 'N521955',
      specPattern: 'NAC',
      orderNumber: 'S86NSS64D',
      cabin: 'NON-AC',
      arm: '2.1M TATT',
      bucket: '0.64_CUM_NON-DGMS',
      dgms: '',
      meshGuard: 'Mesh Guard',
      productionStart: '02/09/2019',
      priority: 'medium'
    },
    {
      id: 7,
      slNo: 7,
      status: 'In Progress',
      revisionNo: 5,
      market: 'DOMESTIC',
      prodMonth: 'Sep 19',
      model: 'PC130-NI@-7',
      machineNumber: 'N521956',
      specPattern: 'NAC',
      orderNumber: 'S86NSS64D',
      cabin: 'NON-AC',
      arm: '2.1M TATT',
      bucket: '0.64_CUM_NON-DGMS',
      dgms: '',
      meshGuard: 'Mesh Guard',
      productionStart: '03/09/2019',
      priority: 'medium'
    },
    {
      id: 8,
      slNo: 8,
      status: 'Kitting Completed',
      revisionNo: 1,
      market: 'EXPORT',
      prodMonth: 'Oct 19',
      model: 'PC210-NI@-8MC',
      machineNumber: 'N721256',
      specPattern: 'NAA',
      orderNumber: 'S67ASS100',
      cabin: 'AC',
      arm: '2.8M TATT',
      bucket: '1.0_CUM_NON-DGMS',
      dgms: '',
      meshGuard: 'Mesh Guard',
      productionStart: '04/09/2019',
      priority: 'high'
    },
    {
      id: 9,
      slNo: 9,
      status: 'Quality Check',
      revisionNo: 3,
      market: 'EXPORT',
      prodMonth: 'Oct 19',
      model: 'PC210-NI@-10M0',
      machineNumber: 'N720117',
      specPattern: 'NCA',
      orderNumber: 'S86ASS900',
      cabin: 'AC',
      arm: '3.0M TATT',
      bucket: '1.0_CUM_NON-DGMS',
      dgms: '',
      meshGuard: 'Mesh Guard Ext Ls',
      productionStart: '05/09/2019',
      priority: 'medium'
    },
    {
      id: 10,
      slNo: 10,
      status: 'Pending',
      revisionNo: 2,
      market: 'EXPORT',
      prodMonth: 'Oct 19',
      model: 'PC210-NI@-10M0',
      machineNumber: 'N720054',
      specPattern: 'NAA',
      orderNumber: 'S77ASS100',
      cabin: 'AC',
      arm: '2.8M TATT',
      bucket: '1.0_CUM_NON-DGMS',
      dgms: '',
      meshGuard: 'Mesh Guard',
      productionStart: '06/09/2019',
      priority: 'medium'
    },
    {
      id: 11,
      slNo: 11,
      status: 'Cancelled',
      revisionNo: 1,
      market: 'DOMESTIC',
      prodMonth: 'Nov 19',
      model: 'PC300-8M0',
      machineNumber: 'N821789',
      specPattern: 'NCA',
      orderNumber: 'S89DEF456',
      cabin: 'AC',
      arm: '3.2M TATT',
      bucket: '1.5_CUM_XL',
      dgms: 'DGMS',
      meshGuard: 'Mesh Guard Ext',
      productionStart: '07/09/2019',
      priority: 'low'
    },
    {
      id: 12,
      slNo: 12,
      status: 'In Progress',
      revisionNo: 4,
      market: 'EXPORT',
      prodMonth: 'Nov 19',
      model: 'PC400-7',
      machineNumber: 'N921890',
      specPattern: 'NAA',
      orderNumber: 'E12XYZ789',
      cabin: 'NON-AC',
      arm: '3.5M',
      bucket: '2.0_CUM_HD',
      dgms: '',
      meshGuard: 'Mesh Guard',
      productionStart: '08/09/2019',
      priority: 'high'
    },
    {
      id: 13,
      slNo: 13,
      status: 'Kitting Completed',
      revisionNo: 2,
      market: 'DOMESTIC',
      prodMonth: 'Dec 19',
      model: 'PC130-NI@-7',
      machineNumber: 'N521957',
      specPattern: 'NAC',
      orderNumber: 'S90ABC123',
      cabin: 'AC',
      arm: '2.1M TATT',
      bucket: '0.64_CUM_NON-DGMS',
      dgms: '',
      meshGuard: 'Mesh Guard',
      productionStart: '09/09/2019',
      priority: 'medium'
    },
    {
      id: 14,
      slNo: 14,
      status: 'On Hold',
      revisionNo: 3,
      market: 'EXPORT',
      prodMonth: 'Dec 19',
      model: 'PC210-NI@-10M0',
      machineNumber: 'N720118',
      specPattern: 'NCA',
      orderNumber: 'E34GHI456',
      cabin: 'AC',
      arm: '3.0M TATT',
      bucket: '1.0_CUM_NON-DGMS',
      dgms: 'DGMS',
      meshGuard: 'Mesh Guard Ext Ls',
      productionStart: '10/09/2019',
      priority: 'high'
    },
    {
      id: 15,
      slNo: 15,
      status: 'Quality Check',
      revisionNo: 5,
      market: 'DOMESTIC',
      prodMonth: 'Dec 19',
      model: 'PC300-8M0',
      machineNumber: 'N821790',
      specPattern: 'NAA',
      orderNumber: 'S91JKL789',
      cabin: 'NON-AC',
      arm: '3.2M',
      bucket: '1.5_CUM_XL',
      dgms: '',
      meshGuard: 'Mesh Guard',
      productionStart: '11/09/2019',
      priority: 'medium'
    }
  ],
  
  dashboardKPIs: {
    totalOrders: 156,
    completedOrders: 89,
    pendingOrders: 45,
    delayedOrders: 22,
    productionEfficiency: 87.5,
    qualityScore: 94.2
  }
};

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = APIService;
}
