<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>KIPL Warehouse Management System</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <!-- Material Icons -->
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  
  <style>
    /* KIPL Original Colors */
    :root {
      --kipl-sidebar-blue: #2F5597;
      --kipl-header-orange: #FFA500;
      --kipl-table-header: #D9E2F3;
      --kipl-row-even: #F2F2F2;
      --kipl-row-odd: #FFFFFF;
      --kipl-border: #8EA9DB;
      --text-primary: #000000;
      --text-white: #FFFFFF;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      font-size: 13px;
      background-color: #F2F2F2;
    }

    .app-container {
      display: flex;
      min-height: 100vh;
    }

    /* Sidebar */
    .sidebar {
      width: 200px;
      background: var(--kipl-sidebar-blue);
      position: fixed;
      height: 100vh;
      left: 0;
      top: 0;
      z-index: 1000;
    }

    .sidebar-header {
      padding: 12px;
      background: var(--kipl-sidebar-blue);
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 60px;
      border-bottom: 1px solid rgba(255,255,255,0.2);
    }

    .logo {
      color: var(--text-white);
      font-weight: bold;
      font-size: 16px;
    }

    .sidebar-nav {
      flex: 1;
      padding: 0;
    }

    .nav-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 16px;
      text-decoration: none;
      color: var(--text-white);
      font-size: 12px;
      border-bottom: 1px solid rgba(255,255,255,0.1);
      transition: all 0.2s ease;
    }

    .nav-item:hover {
      background-color: rgba(255,255,255,0.1);
    }

    .nav-item.active {
      background: rgba(255,255,255,0.2);
      border-left: 4px solid var(--kipl-header-orange);
    }

    /* Main Content */
    .main-content {
      flex: 1;
      margin-left: 200px;
      display: flex;
      flex-direction: column;
    }

    /* Header */
    .app-header {
      background: var(--kipl-header-orange);
      border-bottom: 2px solid #E69500;
      padding: 8px 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      min-height: 50px;
    }

    .page-title {
      font-size: 16px;
      font-weight: bold;
      color: var(--text-white);
      margin: 0;
    }

    .user-profile {
      display: flex;
      align-items: center;
      gap: 8px;
      color: var(--text-white);
      font-size: 12px;
    }

    /* Content Area */
    .content-area {
      flex: 1;
      padding: 8px;
    }

    /* Data Table */
    .data-table-container {
      background: white;
      border: 2px solid var(--kipl-border);
      margin: 4px;
      box-shadow: 1px 1px 3px rgba(0,0,0,0.1);
    }

    .table-header {
      padding: 8px 12px;
      background: var(--kipl-table-header);
      border-bottom: 1px solid var(--kipl-border);
      display: flex;
      align-items: center;
      justify-content: space-between;
      min-height: 32px;
    }

    .table-title {
      font-size: 14px;
      font-weight: bold;
      color: var(--text-primary);
      margin: 0;
    }

    .table-actions {
      display: flex;
      gap: 8px;
      align-items: center;
    }

    .filter-btn {
      background: none;
      border: 1px solid var(--kipl-border);
      padding: 4px 8px;
      font-size: 11px;
      cursor: pointer;
      border-radius: 2px;
    }

    .filter-btn:hover {
      background: var(--kipl-table-header);
    }

    .filter-btn.active {
      background: var(--kipl-sidebar-blue);
      color: white;
    }

    .quick-search {
      padding: 4px 8px;
      border: 1px solid var(--kipl-border);
      font-size: 11px;
      width: 150px;
      border-radius: 2px;
    }

    .add-new-btn {
      background: var(--kipl-header-orange);
      color: white;
      border: none;
      padding: 4px 8px;
      font-size: 11px;
      cursor: pointer;
      border-radius: 2px;
      display: flex;
      align-items: center;
      gap: 4px;
    }

    /* Column Filters */
    .column-filters {
      background: #F8F9FA;
      border-bottom: 1px solid var(--kipl-border);
      padding: 8px 12px;
    }

    .filters-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: bold;
      font-size: 12px;
      color: var(--kipl-sidebar-blue);
      margin-bottom: 8px;
    }

    .filters-row {
      display: flex;
      align-items: end;
      gap: 12px;
      flex-wrap: wrap;
    }

    .filter-group {
      display: flex;
      flex-direction: column;
      gap: 4px;
      min-width: 100px;
    }

    .filter-label {
      font-size: 11px;
      font-weight: bold;
      color: var(--text-primary);
    }

    .filter-select {
      padding: 3px 6px;
      border: 1px solid var(--kipl-border);
      border-radius: 2px;
      font-size: 11px;
      background: white;
    }

    .clear-filters-btn {
      background: #6C757D;
      color: white;
      border: none;
      padding: 4px 8px;
      border-radius: 2px;
      cursor: pointer;
      font-size: 11px;
      display: flex;
      align-items: center;
      gap: 4px;
      height: fit-content;
    }

    .data-table {
      width: 100%;
      border-collapse: separate;
      border-spacing: 0;
      font-size: 12px;
    }

    .data-table th {
      background: var(--kipl-table-header);
      padding: 8px 6px;
      text-align: center;
      font-weight: bold;
      color: var(--text-primary);
      border: 1px solid var(--kipl-border);
      font-size: 12px;
    }

    .data-table td {
      padding: 6px 4px;
      border: 1px solid var(--kipl-border);
      color: var(--text-primary);
      font-size: 12px;
      text-align: center;
    }

    .data-table tr:nth-child(even) {
      background: var(--kipl-row-even);
    }

    .data-table tr:nth-child(odd) {
      background: var(--kipl-row-odd);
    }

    .data-table tr:hover {
      background: #E6F3FF !important;
    }

    /* Status Badges */
    .status-badge {
      display: inline-block;
      padding: 3px 8px;
      font-size: 12px;
      font-weight: normal;
      text-align: center;
      min-width: 100px;
    }

    .status-kitting-completed {
      background: #70AD47;
      color: white;
    }

    .status-in-progress {
      background: #4472C4;
      color: white;
    }

    .status-pending {
      background: #FFC000;
      color: black;
    }

    .status-quality-check {
      background: #7030A0;
      color: white;
    }

    .status-on-hold {
      background: #FF6B35;
      color: white;
    }

    .status-cancelled {
      background: #DC3545;
      color: white;
    }

    /* Action Icons */
    .action-icons {
      display: flex;
      gap: 4px;
      justify-content: center;
    }

    .action-icon {
      font-size: 14px;
      cursor: pointer;
      color: var(--kipl-sidebar-blue);
      padding: 2px;
      border-radius: 2px;
    }

    .action-icon:hover {
      background: rgba(47, 85, 151, 0.1);
    }

    /* Pagination */
    .pagination {
      padding: 8px 12px;
      background: white;
      border-top: 1px solid var(--kipl-border);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .pagination-info {
      font-size: 12px;
      color: #666;
    }

    .pagination-controls {
      display: flex;
      gap: 8px;
    }

    .page-btn {
      background: white;
      border: 1px solid var(--kipl-border);
      padding: 4px 8px;
      cursor: pointer;
      font-size: 12px;
    }

    .page-btn:hover {
      background: var(--kipl-table-header);
    }

    .page-btn.active {
      background: var(--kipl-sidebar-blue);
      color: white;
    }
  </style>
</head>
<body>
  <div class="app-container">
    <!-- Sidebar -->
    <aside class="sidebar">
      <div class="sidebar-header">
        <div class="logo">KOMATSU</div>
      </div>
      
      <nav class="sidebar-nav">
        <a href="#home" class="nav-item">
          <span class="material-icons">home</span>
          <span>Home</span>
        </a>
        <a href="#production-plan" class="nav-item active">
          <span class="material-icons">assignment</span>
          <span>Production Plan</span>
        </a>
        <a href="#firm-order" class="nav-item">
          <span class="material-icons">list_alt</span>
          <span>Firm Order</span>
        </a>
        <a href="#logistics" class="nav-item">
          <span class="material-icons">local_shipping</span>
          <span>Logistics</span>
        </a>
      </nav>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
      <!-- Header -->
      <header class="app-header">
        <h1 class="page-title">Production Plan</h1>
        <div class="user-profile">
          <span class="material-icons">account_circle</span>
          <div>Admin User</div>
        </div>
      </header>

      <!-- Content Area -->
      <div class="content-area">
        <div class="data-table-container">
          <div class="table-header">
            <h2 class="table-title">Production Plan</h2>
            <div class="table-actions">
              <button class="filter-btn active">All</button>
              <button class="filter-btn">Completed</button>
              <button class="filter-btn">In Progress</button>
              <button class="filter-btn">Pending</button>
              <input type="text" class="quick-search" placeholder="Search...">
              <button class="add-new-btn">
                <span class="material-icons">add</span> Add New
              </button>
            </div>
          </div>
          
          <div class="column-filters">
            <div class="filters-title">
              <span class="material-icons">filter_list</span> Column Filters
            </div>
            <div class="filters-row">
              <div class="filter-group">
                <label class="filter-label">Status</label>
                <select class="filter-select">
                  <option value="">All</option>
                  <option value="Kitting Completed">Kitting Completed</option>
                  <option value="In Progress">In Progress</option>
                  <option value="Pending">Pending</option>
                  <option value="Quality Check">Quality Check</option>
                  <option value="On Hold">On Hold</option>
                  <option value="Cancelled">Cancelled</option>
                </select>
              </div>
              <div class="filter-group">
                <label class="filter-label">Market</label>
                <select class="filter-select">
                  <option value="">All</option>
                  <option value="DOMESTIC">DOMESTIC</option>
                  <option value="EXPORT">EXPORT</option>
                </select>
              </div>
              <div class="filter-group">
                <label class="filter-label">Cabin</label>
                <select class="filter-select">
                  <option value="">All</option>
                  <option value="AC">AC</option>
                  <option value="NON-AC">NON-AC</option>
                </select>
              </div>
              <div class="filter-group">
                <label class="filter-label">Revision</label>
                <select class="filter-select">
                  <option value="">All</option>
                  <option value="1">1</option>
                  <option value="2">2</option>
                  <option value="3">3</option>
                  <option value="4">4</option>
                  <option value="5">5</option>
                </select>
              </div>
              <button class="clear-filters-btn">
                <span class="material-icons">clear</span> Clear
              </button>
            </div>
          </div>

          <table class="data-table">
            <thead>
              <tr>
                <th>Actions</th>
                <th>Status</th>
                <th>Revision #</th>
                <th>Market</th>
                <th>Prod Month</th>
                <th>Model</th>
                <th>Machine #</th>
                <th>SPEC Pattern</th>
                <th>Order #</th>
                <th>Cabin</th>
                <th>Arm</th>
                <th>Bucket</th>
                <th>DGMS</th>
                <th>Mesh Guard / Add</th>
                <th>Production Start Date</th>
              </tr>
            </thead>
            <tbody id="tableBody">
              <!-- Data will be loaded here -->
            </tbody>
          </table>

          <div class="pagination">
            <div class="pagination-info">Showing 1-10 of 15 entries</div>
            <div class="pagination-controls">
              <button class="page-btn active">1</button>
              <button class="page-btn">2</button>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>

  <script>
    // Complete data with all status types and revision numbers 1-5
    const data = [
      {
        id: 1,
        status: 'Kitting Completed',
        revisionNo: 1,
        market: 'DOMESTIC',
        prodMonth: 'Sep 19',
        model: 'PC130-NI@-7',
        machineNumber: 'N521950',
        specPattern: 'NAC',
        orderNumber: 'S86NSS64D',
        cabin: 'NON-AC',
        arm: '2.1M',
        bucket: '0.64_CUM_NON-DGMS',
        dgms: '',
        meshGuard: 'Mesh Guard',
        productionStart: '28/08/2019'
      },
      {
        id: 2,
        status: 'In Progress',
        revisionNo: 2,
        market: 'DOMESTIC',
        prodMonth: 'Sep 19',
        model: 'PC130-NI@-7',
        machineNumber: 'N521951',
        specPattern: 'NCA',
        orderNumber: 'S86NSS64D',
        cabin: 'NON-AC',
        arm: '2.1M TATT',
        bucket: '0.64_CUM_NON-DGMS',
        dgms: '',
        meshGuard: 'Mesh Guard',
        productionStart: '29/08/2019'
      },
      {
        id: 3,
        status: 'Pending',
        revisionNo: 1,
        market: 'DOMESTIC',
        prodMonth: 'Sep 19',
        model: 'PC130-NI@-7',
        machineNumber: 'N521952',
        specPattern: 'NAC',
        orderNumber: 'S86NSS64D',
        cabin: 'NON-AC',
        arm: '2.1M TATT',
        bucket: '0.64_CUM_NON-DGMS',
        dgms: '',
        meshGuard: 'Mesh Guard',
        productionStart: '30/08/2019'
      },
      {
        id: 4,
        status: 'Quality Check',
        revisionNo: 3,
        market: 'DOMESTIC',
        prodMonth: 'Sep 19',
        model: 'PC130-NI@-7',
        machineNumber: 'N521953',
        specPattern: 'NAC',
        orderNumber: 'S86NSS64D',
        cabin: 'NON-AC',
        arm: '2.1M TATT',
        bucket: '0.64_CUM_NON-DGMS',
        dgms: '',
        meshGuard: 'Mesh Guard',
        productionStart: '31/08/2019'
      },
      {
        id: 5,
        status: 'Kitting Completed',
        revisionNo: 4,
        market: 'DOMESTIC',
        prodMonth: 'Sep 19',
        model: 'PC130-NI@-7',
        machineNumber: 'N521954',
        specPattern: 'NAC',
        orderNumber: 'S86NSS64D',
        cabin: 'NON-AC',
        arm: '2.1M TATT',
        bucket: '0.64_CUM_NON-DGMS',
        dgms: '',
        meshGuard: 'Mesh Guard',
        productionStart: '01/09/2019'
      },
      {
        id: 6,
        status: 'On Hold',
        revisionNo: 2,
        market: 'DOMESTIC',
        prodMonth: 'Sep 19',
        model: 'PC130-NI@-7',
        machineNumber: 'N521955',
        specPattern: 'NAC',
        orderNumber: 'S86NSS64D',
        cabin: 'NON-AC',
        arm: '2.1M TATT',
        bucket: '0.64_CUM_NON-DGMS',
        dgms: '',
        meshGuard: 'Mesh Guard',
        productionStart: '02/09/2019'
      },
      {
        id: 7,
        status: 'In Progress',
        revisionNo: 5,
        market: 'DOMESTIC',
        prodMonth: 'Sep 19',
        model: 'PC130-NI@-7',
        machineNumber: 'N521956',
        specPattern: 'NAC',
        orderNumber: 'S86NSS64D',
        cabin: 'NON-AC',
        arm: '2.1M TATT',
        bucket: '0.64_CUM_NON-DGMS',
        dgms: '',
        meshGuard: 'Mesh Guard',
        productionStart: '03/09/2019'
      },
      {
        id: 8,
        status: 'Kitting Completed',
        revisionNo: 1,
        market: 'EXPORT',
        prodMonth: 'Oct 19',
        model: 'PC210-NI@-8MC',
        machineNumber: 'N721256',
        specPattern: 'NAA',
        orderNumber: 'S67ASS100',
        cabin: 'AC',
        arm: '2.8M TATT',
        bucket: '1.0_CUM_NON-DGMS',
        dgms: '',
        meshGuard: 'Mesh Guard',
        productionStart: '04/09/2019'
      },
      {
        id: 9,
        status: 'Quality Check',
        revisionNo: 3,
        market: 'EXPORT',
        prodMonth: 'Oct 19',
        model: 'PC210-NI@-10M0',
        machineNumber: 'N720117',
        specPattern: 'NCA',
        orderNumber: 'S86ASS900',
        cabin: 'AC',
        arm: '3.0M TATT',
        bucket: '1.0_CUM_NON-DGMS',
        dgms: '',
        meshGuard: 'Mesh Guard Ext Ls',
        productionStart: '05/09/2019'
      },
      {
        id: 10,
        status: 'Pending',
        revisionNo: 2,
        market: 'EXPORT',
        prodMonth: 'Oct 19',
        model: 'PC210-NI@-10M0',
        machineNumber: 'N720054',
        specPattern: 'NAA',
        orderNumber: 'S77ASS100',
        cabin: 'AC',
        arm: '2.8M TATT',
        bucket: '1.0_CUM_NON-DGMS',
        dgms: '',
        meshGuard: 'Mesh Guard',
        productionStart: '06/09/2019'
      },
      {
        id: 11,
        status: 'Cancelled',
        revisionNo: 1,
        market: 'DOMESTIC',
        prodMonth: 'Nov 19',
        model: 'PC300-8M0',
        machineNumber: 'N821789',
        specPattern: 'NCA',
        orderNumber: 'S89DEF456',
        cabin: 'AC',
        arm: '3.2M TATT',
        bucket: '1.5_CUM_XL',
        dgms: 'DGMS',
        meshGuard: 'Mesh Guard Ext',
        productionStart: '07/09/2019'
      },
      {
        id: 12,
        status: 'In Progress',
        revisionNo: 4,
        market: 'EXPORT',
        prodMonth: 'Nov 19',
        model: 'PC400-7',
        machineNumber: 'N921890',
        specPattern: 'NAA',
        orderNumber: 'E12XYZ789',
        cabin: 'NON-AC',
        arm: '3.5M',
        bucket: '2.0_CUM_HD',
        dgms: '',
        meshGuard: 'Mesh Guard',
        productionStart: '08/09/2019'
      },
      {
        id: 13,
        status: 'Kitting Completed',
        revisionNo: 2,
        market: 'DOMESTIC',
        prodMonth: 'Dec 19',
        model: 'PC130-NI@-7',
        machineNumber: 'N521957',
        specPattern: 'NAC',
        orderNumber: 'S90ABC123',
        cabin: 'AC',
        arm: '2.1M TATT',
        bucket: '0.64_CUM_NON-DGMS',
        dgms: '',
        meshGuard: 'Mesh Guard',
        productionStart: '09/09/2019'
      },
      {
        id: 14,
        status: 'On Hold',
        revisionNo: 3,
        market: 'EXPORT',
        prodMonth: 'Dec 19',
        model: 'PC210-NI@-10M0',
        machineNumber: 'N720118',
        specPattern: 'NCA',
        orderNumber: 'E34GHI456',
        cabin: 'AC',
        arm: '3.0M TATT',
        bucket: '1.0_CUM_NON-DGMS',
        dgms: 'DGMS',
        meshGuard: 'Mesh Guard Ext Ls',
        productionStart: '10/09/2019'
      },
      {
        id: 15,
        status: 'Quality Check',
        revisionNo: 5,
        market: 'DOMESTIC',
        prodMonth: 'Dec 19',
        model: 'PC300-8M0',
        machineNumber: 'N821790',
        specPattern: 'NAA',
        orderNumber: 'S91JKL789',
        cabin: 'NON-AC',
        arm: '3.2M',
        bucket: '1.5_CUM_XL',
        dgms: '',
        meshGuard: 'Mesh Guard',
        productionStart: '11/09/2019'
      }
    ];

    function createStatusBadge(status) {
      const statusClass = status.toLowerCase().replace(/\s+/g, '-');
      return `<span class="status-badge status-${statusClass}">${status}</span>`;
    }

    function createActionIcons(row) {
      return `
        <div class="action-icons">
          <span class="material-icons action-icon" title="View Production Details">visibility</span>
          <span class="material-icons action-icon" title="Edit Production Plan">edit</span>
          <span class="material-icons action-icon" title="Copy Production Plan">content_copy</span>
          <span class="material-icons action-icon" title="Delete Production Plan">delete</span>
        </div>
      `;
    }

    let currentData = data;
    let currentPage = 1;
    const itemsPerPage = 10;

    function loadTable(dataToShow = currentData) {
      const tbody = document.getElementById('tableBody');
      const startIndex = (currentPage - 1) * itemsPerPage;
      const endIndex = startIndex + itemsPerPage;
      const pageData = dataToShow.slice(startIndex, endIndex);

      tbody.innerHTML = pageData.map(row => `
        <tr>
          <td>${createActionIcons(row)}</td>
          <td>${createStatusBadge(row.status)}</td>
          <td>${row.revisionNo}</td>
          <td>${row.market}</td>
          <td>${row.prodMonth}</td>
          <td>${row.model}</td>
          <td>${row.machineNumber}</td>
          <td>${row.specPattern}</td>
          <td>${row.orderNumber}</td>
          <td>${row.cabin}</td>
          <td>${row.arm}</td>
          <td>${row.bucket}</td>
          <td>${row.dgms}</td>
          <td>${row.meshGuard}</td>
          <td>${row.productionStart}</td>
        </tr>
      `).join('');

      updatePagination(dataToShow);
    }

    function updatePagination(dataToShow) {
      const totalPages = Math.ceil(dataToShow.length / itemsPerPage);
      const startItem = ((currentPage - 1) * itemsPerPage) + 1;
      const endItem = Math.min(currentPage * itemsPerPage, dataToShow.length);

      document.querySelector('.pagination-info').textContent =
        `Showing ${startItem}-${endItem} of ${dataToShow.length} entries`;

      const controls = document.querySelector('.pagination-controls');
      controls.innerHTML = '';

      for (let i = 1; i <= totalPages; i++) {
        const btn = document.createElement('button');
        btn.className = `page-btn ${i === currentPage ? 'active' : ''}`;
        btn.textContent = i;
        btn.onclick = () => {
          currentPage = i;
          loadTable(dataToShow);
        };
        controls.appendChild(btn);
      }
    }

    function filterByStatus(status) {
      // Update active button
      document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
      event.target.classList.add('active');

      if (status === 'All') {
        currentData = data;
      } else {
        currentData = data.filter(row => row.status.includes(status));
      }

      currentPage = 1;
      loadTable(currentData);
    }

    function filterByColumn(column, value) {
      if (!value) {
        currentData = data;
      } else {
        currentData = data.filter(row =>
          row[column] && row[column].toString().toLowerCase().includes(value.toLowerCase())
        );
      }
      currentPage = 1;
      loadTable(currentData);
    }

    function clearFilters() {
      // Reset all filters
      document.querySelectorAll('.filter-select').forEach(select => select.value = '');
      document.querySelector('.quick-search').value = '';

      // Reset status filter
      document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
      document.querySelector('.filter-btn').classList.add('active');

      currentData = data;
      currentPage = 1;
      loadTable(currentData);
    }

    function setupEventListeners() {
      // Status filter buttons
      document.querySelectorAll('.filter-btn').forEach((btn, index) => {
        const statuses = ['All', 'Completed', 'Progress', 'Pending'];
        btn.onclick = () => filterByStatus(statuses[index]);
      });

      // Column filters
      document.querySelectorAll('.filter-select').forEach(select => {
        select.onchange = (e) => {
          const column = e.target.parentElement.querySelector('.filter-label').textContent.toLowerCase();
          const columnMap = {
            'status': 'status',
            'market': 'market',
            'cabin': 'cabin',
            'revision': 'revisionNo'
          };
          filterByColumn(columnMap[column], e.target.value);
        };
      });

      // Global search
      document.querySelector('.quick-search').oninput = (e) => {
        const query = e.target.value.toLowerCase();
        if (!query) {
          currentData = data;
        } else {
          currentData = data.filter(row =>
            Object.values(row).some(value =>
              value.toString().toLowerCase().includes(query)
            )
          );
        }
        currentPage = 1;
        loadTable(currentData);
      };

      // Clear filters button
      document.querySelector('.clear-filters-btn').onclick = clearFilters;

      // Action icons
      document.addEventListener('click', (e) => {
        if (e.target.classList.contains('action-icon')) {
          const action = e.target.textContent;
          const row = e.target.closest('tr');
          const rowIndex = Array.from(row.parentElement.children).indexOf(row);
          const rowData = currentData[rowIndex];

          switch(action) {
            case 'visibility':
              alert(`View details for ${rowData.orderNumber}`);
              break;
            case 'edit':
              alert(`Edit ${rowData.orderNumber}`);
              break;
            case 'content_copy':
              alert(`Copy ${rowData.orderNumber}`);
              break;
            case 'delete':
              if (confirm(`Delete ${rowData.orderNumber}?`)) {
                alert(`${rowData.orderNumber} deleted`);
              }
              break;
          }
        }
      });
    }

    // Initialize
    document.addEventListener('DOMContentLoaded', () => {
      loadTable();
      setupEventListeners();
    });
  </script>
</body>
</html>
