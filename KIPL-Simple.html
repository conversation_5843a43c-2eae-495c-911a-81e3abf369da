<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>KIPL Warehouse Management System</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <!-- Material Icons -->
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  
  <style>
    /* KIPL Original Colors */
    :root {
      --kipl-sidebar-blue: #2F5597;
      --kipl-header-orange: #FFA500;
      --kipl-table-header: #D9E2F3;
      --kipl-row-even: #F2F2F2;
      --kipl-row-odd: #FFFFFF;
      --kipl-border: #8EA9DB;
      --text-primary: #000000;
      --text-white: #FFFFFF;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      font-size: 14px;
      background-color: #F2F2F2;
      line-height: 1.4;
    }

    .app-container {
      display: flex;
      min-height: 100vh;
    }

    /* Sidebar */
    .sidebar {
      width: 200px;
      background: var(--kipl-sidebar-blue);
      position: fixed;
      height: 100vh;
      left: 0;
      top: 0;
      z-index: 1000;
    }

    .sidebar-header {
      padding: 12px;
      background: var(--kipl-sidebar-blue);
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 60px;
      border-bottom: 1px solid rgba(255,255,255,0.2);
    }

    .logo {
      color: var(--text-white);
      font-weight: bold;
      font-size: 16px;
    }

    .sidebar-nav {
      flex: 1;
      padding: 0;
    }

    .nav-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 16px;
      text-decoration: none;
      color: var(--text-white);
      font-size: 13px;
      border-bottom: 1px solid rgba(255,255,255,0.1);
      transition: all 0.2s ease;
    }

    .nav-item:hover {
      background-color: rgba(255,255,255,0.1);
    }

    .nav-item.active {
      background: rgba(255,255,255,0.2);
      border-left: 4px solid var(--kipl-header-orange);
    }

    /* Tree Structure Navigation */
    .nav-group {
      margin-bottom: 0;
    }

    .nav-parent {
      cursor: pointer;
      position: relative;
      justify-content: space-between;
    }

    .nav-parent:hover {
      background-color: rgba(255,255,255,0.1);
    }

    .nav-arrow {
      font-size: 16px !important;
      transition: transform 0.3s ease;
    }

    .nav-group.expanded .nav-arrow {
      transform: rotate(180deg);
    }

    .nav-children {
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.3s ease;
      background: rgba(0,0,0,0.1);
    }

    .nav-group.expanded .nav-children {
      max-height: 300px;
    }

    .nav-child {
      padding-left: 8px;
      font-size: 11px;
      border-bottom: 1px solid rgba(255,255,255,0.05);
    }

    .nav-child:hover {
      background-color: rgba(255,255,255,0.1);
    }

    .nav-child.active {
      background: rgba(255,255,255,0.15);
      border-left: 3px solid var(--kipl-header-orange);
    }

    .nav-indent {
      width: 16px;
      height: 1px;
      border-bottom: 1px solid rgba(255,255,255,0.2);
      margin-right: 8px;
      position: relative;
    }

    .nav-indent::before {
      content: '';
      position: absolute;
      left: 0;
      top: -6px;
      width: 1px;
      height: 12px;
      background: rgba(255,255,255,0.2);
    }

    .nav-child .material-icons {
      font-size: 14px;
    }

    /* Main Content */
    .main-content {
      flex: 1;
      margin-left: 200px;
      display: flex;
      flex-direction: column;
    }

    /* Header */
    .app-header {
      background: var(--kipl-header-orange);
      border-bottom: 2px solid #E69500;
      padding: 8px 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      min-height: 50px;
    }

    .page-title {
      font-size: 18px;
      font-weight: bold;
      color: var(--text-white);
      margin: 0;
    }

    .user-profile {
      display: flex;
      align-items: center;
      gap: 8px;
      color: var(--text-white);
      font-size: 12px;
    }

    /* Content Area */
    .content-area {
      flex: 1;
      padding: 8px;
    }

    /* Data Table */
    .data-table-container {
      background: white;
      border: 2px solid var(--kipl-border);
      margin: 4px;
      box-shadow: 1px 1px 3px rgba(0,0,0,0.1);
    }

    .table-header {
      padding: 8px 12px;
      background: var(--kipl-table-header);
      border-bottom: 1px solid var(--kipl-border);
      display: flex;
      align-items: center;
      justify-content: space-between;
      min-height: 32px;
    }

    .table-title {
      font-size: 16px;
      font-weight: bold;
      color: var(--text-primary);
      margin: 0;
    }

    .table-actions {
      display: flex;
      gap: 8px;
      align-items: center;
    }

    .filter-btn {
      background: none;
      border: 1px solid var(--kipl-border);
      padding: 4px 8px;
      font-size: 11px;
      cursor: pointer;
      border-radius: 2px;
    }

    .filter-btn:hover {
      background: var(--kipl-table-header);
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .filter-btn.active {
      background: var(--kipl-sidebar-blue);
      color: white;
      box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .filter-btn {
      transition: all 0.2s ease;
    }

    .quick-search {
      padding: 4px 8px;
      border: 1px solid var(--kipl-border);
      font-size: 11px;
      width: 150px;
      border-radius: 2px;
    }

    .add-new-btn {
      background: var(--kipl-header-orange);
      color: white;
      border: none;
      padding: 4px 8px;
      font-size: 11px;
      cursor: pointer;
      border-radius: 2px;
      display: flex;
      align-items: center;
      gap: 4px;
      transition: all 0.2s ease;
      font-weight: bold;
    }

    .add-new-btn:hover {
      background: #E69500;
      transform: translateY(-1px);
      box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    }

    .add-new-btn:active {
      transform: translateY(0);
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    /* Modern Search & Filters */
    .modern-filters {
      background: linear-gradient(135deg, #F8F9FA, #FFFFFF);
      border-bottom: 2px solid var(--kipl-border);
      padding: 16px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .filters-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }

    .filters-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: bold;
      font-size: 16px;
      color: var(--kipl-sidebar-blue);
    }

    .clear-all-btn {
      background: #DC3545;
      color: white;
      border: none;
      padding: 8px 12px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 13px;
      display: flex;
      align-items: center;
      gap: 6px;
      font-weight: 500;
      transition: all 0.2s ease;
    }

    .clear-all-btn:hover {
      background: #C82333;
      transform: translateY(-1px);
      box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    }

    .filters-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
    }

    .search-group {
      display: flex;
      flex-direction: column;
      gap: 6px;
    }

    .search-label {
      font-size: 13px;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 2px;
    }

    .search-input-container {
      position: relative;
    }

    .modern-search-input {
      width: 100%;
      padding: 10px 12px 10px 40px;
      border: 2px solid #E1E5E9;
      border-radius: 8px;
      font-size: 13px;
      background: white;
      transition: all 0.2s ease;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .modern-search-input:focus {
      outline: none;
      border-color: var(--kipl-sidebar-blue);
      box-shadow: 0 0 0 3px rgba(47, 85, 151, 0.1);
      transform: translateY(-1px);
    }

    .modern-search-input:hover {
      border-color: #C1C7CD;
    }

    .search-icon {
      position: absolute;
      left: 12px;
      top: 50%;
      transform: translateY(-50%);
      color: #6C757D;
      font-size: 18px;
      pointer-events: none;
    }

    .search-dropdown {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: white;
      border: 2px solid var(--kipl-sidebar-blue);
      border-top: none;
      border-radius: 0 0 8px 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 1000;
      display: none;
      max-height: 200px;
      overflow-y: auto;
    }

    .search-dropdown.show {
      display: block;
    }

    .dropdown-item {
      padding: 10px 12px;
      cursor: pointer;
      font-size: 13px;
      transition: background-color 0.2s ease;
      border-bottom: 1px solid #F1F3F4;
    }

    .dropdown-item:hover {
      background: var(--kipl-table-header);
    }

    .dropdown-item:last-child {
      border-bottom: none;
    }

    .dropdown-item.selected {
      background: var(--kipl-sidebar-blue);
      color: white;
    }

    .data-table {
      width: 100%;
      border-collapse: separate;
      border-spacing: 0;
      font-size: 13px;
    }

    .data-table th {
      background: var(--kipl-table-header);
      padding: 12px 8px;
      text-align: center;
      font-weight: bold;
      color: var(--text-primary);
      border: 1px solid var(--kipl-border);
      font-size: 13px;
    }

    .data-table td {
      padding: 10px 6px;
      border: 1px solid var(--kipl-border);
      color: var(--text-primary);
      font-size: 13px;
      text-align: center;
    }

    .data-table tr:nth-child(even) {
      background: var(--kipl-row-even);
    }

    .data-table tr:nth-child(odd) {
      background: var(--kipl-row-odd);
    }

    .data-table tr:hover {
      background: #E6F3FF !important;
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      transition: all 0.2s ease;
    }

    .data-table tr {
      transition: all 0.2s ease;
    }

    /* Status Badges */
    .status-badge {
      display: inline-block;
      padding: 3px 8px;
      font-size: 12px;
      font-weight: normal;
      text-align: center;
      min-width: 100px;
    }

    .status-kitting-completed {
      background: #70AD47;
      color: white;
    }

    .status-in-progress {
      background: #4472C4;
      color: white;
    }

    .status-pending {
      background: #FFC000;
      color: black;
    }

    .status-quality-check {
      background: #7030A0;
      color: white;
    }

    .status-on-hold {
      background: #FF6B35;
      color: white;
    }

    .status-cancelled {
      background: #DC3545;
      color: white;
    }

    /* Action Icons */
    .action-icons {
      display: flex;
      gap: 4px;
      justify-content: center;
    }

    .action-icon {
      font-size: 14px;
      cursor: pointer;
      color: var(--kipl-sidebar-blue);
      padding: 2px;
      border-radius: 2px;
    }

    .action-icon:hover {
      background: rgba(47, 85, 151, 0.1);
      transform: scale(1.1);
      border-radius: 50%;
    }

    .action-icon {
      transition: all 0.2s ease;
      border-radius: 2px;
    }

    /* Pagination */
    .pagination {
      padding: 8px 12px;
      background: white;
      border-top: 1px solid var(--kipl-border);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .pagination-info {
      font-size: 12px;
      color: #666;
    }

    .pagination-controls {
      display: flex;
      gap: 8px;
    }

    .page-btn {
      background: white;
      border: 1px solid var(--kipl-border);
      padding: 4px 8px;
      cursor: pointer;
      font-size: 12px;
    }

    .page-btn:hover {
      background: var(--kipl-table-header);
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .page-btn.active {
      background: var(--kipl-sidebar-blue);
      color: white;
      box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .page-btn {
      transition: all 0.2s ease;
    }

    /* Modern Modal Styles */
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      display: none;
      align-items: center;
      justify-content: center;
      z-index: 10000;
      backdrop-filter: blur(2px);
    }

    .modal-overlay.show {
      display: flex;
      animation: fadeIn 0.3s ease;
    }

    .modal {
      background: white;
      border-radius: 8px;
      padding: 24px;
      width: 90%;
      max-width: 600px;
      max-height: 80vh;
      overflow-y: auto;
      box-shadow: 0 10px 30px rgba(0,0,0,0.3);
      animation: slideIn 0.3s ease;
    }

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 12px;
      border-bottom: 2px solid var(--kipl-table-header);
    }

    .modal-title {
      font-size: 18px;
      font-weight: bold;
      color: var(--kipl-sidebar-blue);
      margin: 0;
    }

    .modal-close {
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      color: #666;
      padding: 0;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: all 0.2s ease;
    }

    .modal-close:hover {
      background: #f0f0f0;
      color: #333;
    }

    .form-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      margin-bottom: 20px;
    }

    .form-group {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .form-group.full-width {
      grid-column: 1 / -1;
    }

    .form-label {
      font-size: 12px;
      font-weight: bold;
      color: var(--text-primary);
    }

    .form-input,
    .form-select {
      padding: 8px 12px;
      border: 2px solid var(--kipl-border);
      border-radius: 4px;
      font-size: 13px;
      transition: all 0.2s ease;
    }

    .form-input:focus,
    .form-select:focus {
      outline: none;
      border-color: var(--kipl-sidebar-blue);
      box-shadow: 0 0 0 3px rgba(47, 85, 151, 0.1);
    }

    .modal-actions {
      display: flex;
      gap: 12px;
      justify-content: flex-end;
      padding-top: 16px;
      border-top: 1px solid var(--kipl-border);
    }

    .btn-primary {
      background: var(--kipl-sidebar-blue);
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 13px;
      font-weight: bold;
      transition: all 0.2s ease;
    }

    .btn-primary:hover {
      background: #1F4587;
      transform: translateY(-1px);
      box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    }

    .btn-secondary {
      background: #6C757D;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 13px;
      transition: all 0.2s ease;
    }

    .btn-secondary:hover {
      background: #5A6268;
      transform: translateY(-1px);
      box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    }

    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }

    @keyframes slideIn {
      from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
      }
      to {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
    }

    /* Modern Loading Animation */
    .loading-dots {
      display: inline-flex;
      gap: 4px;
      align-items: center;
    }

    .loading-dots span {
      width: 6px;
      height: 6px;
      background: currentColor;
      border-radius: 50%;
      animation: bounce 1.4s ease-in-out infinite both;
    }

    .loading-dots span:nth-child(1) { animation-delay: -0.32s; }
    .loading-dots span:nth-child(2) { animation-delay: -0.16s; }

    @keyframes bounce {
      0%, 80%, 100% {
        transform: scale(0);
      } 40% {
        transform: scale(1);
      }
    }

    @keyframes slideInRight {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }

    @keyframes slideOutRight {
      from {
        transform: translateX(0);
        opacity: 1;
      }
      to {
        transform: translateX(100%);
        opacity: 0;
      }
    }
  </style>
</head>
<body>
  <div class="app-container">
    <!-- Sidebar -->
    <aside class="sidebar">
      <div class="sidebar-header">
        <div class="logo">KOMATSU</div>
      </div>
      
      <nav class="sidebar-nav">
        <div class="nav-item">
          <span class="material-icons">home</span>
          <span>Home</span>
        </div>

        <div class="nav-group">
          <div class="nav-item nav-parent" onclick="toggleNavGroup(this)">
            <span class="material-icons">assignment</span>
            <span>Production Plan</span>
            <span class="material-icons nav-arrow">expand_more</span>
          </div>
          <div class="nav-children">
            <div class="nav-item nav-child active">
              <span class="nav-indent"></span>
              <span class="material-icons">view_list</span>
              <span>Production Plan</span>
            </div>
            <div class="nav-item nav-child">
              <span class="nav-indent"></span>
              <span class="material-icons">schedule</span>
              <span>Production Schedule</span>
            </div>
            <div class="nav-item nav-child">
              <span class="nav-indent"></span>
              <span class="material-icons">assessment</span>
              <span>Production Reports</span>
            </div>
          </div>
        </div>

        <div class="nav-group">
          <div class="nav-item nav-parent" onclick="toggleNavGroup(this)">
            <span class="material-icons">list_alt</span>
            <span>Firm Order</span>
            <span class="material-icons nav-arrow">expand_more</span>
          </div>
          <div class="nav-children">
            <div class="nav-item nav-child">
              <span class="nav-indent"></span>
              <span class="material-icons">shopping_cart</span>
              <span>Order Management</span>
            </div>
            <div class="nav-item nav-child">
              <span class="nav-indent"></span>
              <span class="material-icons">receipt</span>
              <span>Order Tracking</span>
            </div>
            <div class="nav-item nav-child">
              <span class="nav-indent"></span>
              <span class="material-icons">history</span>
              <span>Order History</span>
            </div>
          </div>
        </div>

        <div class="nav-group">
          <div class="nav-item nav-parent" onclick="toggleNavGroup(this)">
            <span class="material-icons">local_shipping</span>
            <span>Logistics</span>
            <span class="material-icons nav-arrow">expand_more</span>
          </div>
          <div class="nav-children">
            <div class="nav-item nav-child">
              <span class="nav-indent"></span>
              <span class="material-icons">inventory</span>
              <span>Warehouse</span>
            </div>
            <div class="nav-item nav-child">
              <span class="nav-indent"></span>
              <span class="material-icons">local_shipping</span>
              <span>Shipping</span>
            </div>
            <div class="nav-item nav-child">
              <span class="nav-indent"></span>
              <span class="material-icons">track_changes</span>
              <span>Tracking</span>
            </div>
          </div>
        </div>

        <div class="nav-group">
          <div class="nav-item nav-parent" onclick="toggleNavGroup(this)">
            <span class="material-icons">build</span>
            <span>Kitting</span>
            <span class="material-icons nav-arrow">expand_more</span>
          </div>
          <div class="nav-children">
            <div class="nav-item nav-child">
              <span class="nav-indent"></span>
              <span class="material-icons">construction</span>
              <span>Kitting Process</span>
            </div>
            <div class="nav-item nav-child">
              <span class="nav-indent"></span>
              <span class="material-icons">check_circle</span>
              <span>Quality Control</span>
            </div>
            <div class="nav-item nav-child">
              <span class="nav-indent"></span>
              <span class="material-icons">inventory_2</span>
              <span>Parts Management</span>
            </div>
          </div>
        </div>

        <div class="nav-group">
          <div class="nav-item nav-parent" onclick="toggleNavGroup(this)">
            <span class="material-icons">analytics</span>
            <span>Reports</span>
            <span class="material-icons nav-arrow">expand_more</span>
          </div>
          <div class="nav-children">
            <div class="nav-item nav-child">
              <span class="nav-indent"></span>
              <span class="material-icons">bar_chart</span>
              <span>Production Reports</span>
            </div>
            <div class="nav-item nav-child">
              <span class="nav-indent"></span>
              <span class="material-icons">pie_chart</span>
              <span>Analytics</span>
            </div>
            <div class="nav-item nav-child">
              <span class="nav-indent"></span>
              <span class="material-icons">download</span>
              <span>Export Data</span>
            </div>
          </div>
        </div>

        <div class="nav-item">
          <span class="material-icons">settings</span>
          <span>Settings</span>
        </div>
      </nav>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
      <!-- Header -->
      <header class="app-header">
        <h1 class="page-title">Production Plan</h1>
        <div class="user-profile">
          <span class="material-icons">account_circle</span>
          <div>Admin User</div>
        </div>
      </header>

      <!-- Content Area -->
      <div class="content-area">
        <div class="data-table-container">
          <div class="table-header">
            <h2 class="table-title">Production Plan</h2>
            <div class="table-actions">
              <button class="filter-btn active">All</button>
              <button class="filter-btn">Completed</button>
              <button class="filter-btn">In Progress</button>
              <button class="filter-btn">Pending</button>
              <input type="text" class="quick-search" placeholder="Search...">
              <button class="add-new-btn">
                <span class="material-icons">add</span> Add New
              </button>
            </div>
          </div>
          
          <div class="modern-filters">
            <div class="filters-header">
              <div class="filters-title">
                <span class="material-icons">search</span>
                <span>Smart Search & Filters</span>
              </div>
              <button class="clear-all-btn" onclick="clearAllFilters()">
                <span class="material-icons">clear_all</span>
                Clear All
              </button>
            </div>

            <div class="filters-grid">
              <div class="search-group">
                <label class="search-label">Status</label>
                <div class="search-input-container">
                  <span class="material-icons search-icon">search</span>
                  <input type="text" class="modern-search-input" placeholder="Search status..." data-column="status">
                  <div class="search-dropdown" data-column="status">
                    <div class="dropdown-item" data-value="">All Statuses</div>
                    <div class="dropdown-item" data-value="Kitting Completed">Kitting Completed</div>
                    <div class="dropdown-item" data-value="In Progress">In Progress</div>
                    <div class="dropdown-item" data-value="Pending">Pending</div>
                    <div class="dropdown-item" data-value="Quality Check">Quality Check</div>
                    <div class="dropdown-item" data-value="On Hold">On Hold</div>
                    <div class="dropdown-item" data-value="Cancelled">Cancelled</div>
                  </div>
                </div>
              </div>

              <div class="search-group">
                <label class="search-label">Market</label>
                <div class="search-input-container">
                  <span class="material-icons search-icon">public</span>
                  <input type="text" class="modern-search-input" placeholder="Search market..." data-column="market">
                  <div class="search-dropdown" data-column="market">
                    <div class="dropdown-item" data-value="">All Markets</div>
                    <div class="dropdown-item" data-value="DOMESTIC">DOMESTIC</div>
                    <div class="dropdown-item" data-value="EXPORT">EXPORT</div>
                  </div>
                </div>
              </div>

              <div class="search-group">
                <label class="search-label">Model</label>
                <div class="search-input-container">
                  <span class="material-icons search-icon">precision_manufacturing</span>
                  <input type="text" class="modern-search-input" placeholder="Search model..." data-column="model">
                </div>
              </div>

              <div class="search-group">
                <label class="search-label">Machine #</label>
                <div class="search-input-container">
                  <span class="material-icons search-icon">tag</span>
                  <input type="text" class="modern-search-input" placeholder="Search machine..." data-column="machineNumber">
                </div>
              </div>

              <div class="search-group">
                <label class="search-label">Order #</label>
                <div class="search-input-container">
                  <span class="material-icons search-icon">receipt_long</span>
                  <input type="text" class="modern-search-input" placeholder="Search order..." data-column="orderNumber">
                </div>
              </div>

              <div class="search-group">
                <label class="search-label">Cabin</label>
                <div class="search-input-container">
                  <span class="material-icons search-icon">airline_seat_recline_normal</span>
                  <input type="text" class="modern-search-input" placeholder="Search cabin..." data-column="cabin">
                  <div class="search-dropdown" data-column="cabin">
                    <div class="dropdown-item" data-value="">All Cabins</div>
                    <div class="dropdown-item" data-value="AC">AC</div>
                    <div class="dropdown-item" data-value="NON-AC">NON-AC</div>
                  </div>
                </div>
              </div>

              <div class="search-group">
                <label class="search-label">DGMS</label>
                <div class="search-input-container">
                  <span class="material-icons search-icon">settings</span>
                  <input type="text" class="modern-search-input" placeholder="Search DGMS..." data-column="dgms">
                  <div class="search-dropdown" data-column="dgms">
                    <div class="dropdown-item" data-value="">All DGMS</div>
                    <div class="dropdown-item" data-value="DGMS">DGMS</div>
                    <div class="dropdown-item" data-value="NON-DGMS">NON-DGMS</div>
                  </div>
                </div>
              </div>

              <div class="search-group">
                <label class="search-label">Revision #</label>
                <div class="search-input-container">
                  <span class="material-icons search-icon">history</span>
                  <input type="text" class="modern-search-input" placeholder="Search revision..." data-column="revisionNo">
                  <div class="search-dropdown" data-column="revisionNo">
                    <div class="dropdown-item" data-value="">All Revisions</div>
                    <div class="dropdown-item" data-value="1">Revision 1</div>
                    <div class="dropdown-item" data-value="2">Revision 2</div>
                    <div class="dropdown-item" data-value="3">Revision 3</div>
                    <div class="dropdown-item" data-value="4">Revision 4</div>
                    <div class="dropdown-item" data-value="5">Revision 5</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <table class="data-table">
            <thead>
              <tr>
                <th>Actions</th>
                <th>Status</th>
                <th>Revision #</th>
                <th>Market</th>
                <th>Prod Month</th>
                <th>Model</th>
                <th>Machine #</th>
                <th>SPEC Pattern</th>
                <th>Order #</th>
                <th>Cabin</th>
                <th>Arm</th>
                <th>Bucket</th>
                <th>DGMS</th>
                <th>Mesh Guard / Add</th>
                <th>Production Start Date</th>
              </tr>
            </thead>
            <tbody id="tableBody">
              <!-- Data will be loaded here -->
            </tbody>
          </table>

          <div class="pagination">
            <div class="pagination-info">Showing 1-10 of 50 entries</div>
            <div class="pagination-controls">
              <button class="page-btn active">1</button>
              <button class="page-btn">2</button>
              <button class="page-btn">3</button>
              <button class="page-btn">4</button>
              <button class="page-btn">5</button>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>

  <!-- Add New Production Plan Modal -->
  <div id="addNewModal" class="modal-overlay">
    <div class="modal">
      <div class="modal-header">
        <h3 class="modal-title">Add New Production Plan</h3>
        <button class="modal-close" onclick="closeAddNewModal()">&times;</button>
      </div>

      <form id="addNewForm">
        <div class="form-grid">
          <div class="form-group">
            <label class="form-label">Status</label>
            <select class="form-select" name="status" required>
              <option value="">Select Status</option>
              <option value="Kitting Completed">Kitting Completed</option>
              <option value="In Progress">In Progress</option>
              <option value="Pending">Pending</option>
              <option value="Quality Check">Quality Check</option>
              <option value="On Hold">On Hold</option>
              <option value="Cancelled">Cancelled</option>
            </select>
          </div>

          <div class="form-group">
            <label class="form-label">Revision #</label>
            <select class="form-select" name="revisionNo" required>
              <option value="">Select Revision</option>
              <option value="1">1</option>
              <option value="2">2</option>
              <option value="3">3</option>
              <option value="4">4</option>
              <option value="5">5</option>
            </select>
          </div>

          <div class="form-group">
            <label class="form-label">Market</label>
            <select class="form-select" name="market" required>
              <option value="">Select Market</option>
              <option value="DOMESTIC">DOMESTIC</option>
              <option value="EXPORT">EXPORT</option>
            </select>
          </div>

          <div class="form-group">
            <label class="form-label">Prod Month</label>
            <input type="text" class="form-input" name="prodMonth" placeholder="e.g., Sep 19" required>
          </div>

          <div class="form-group">
            <label class="form-label">Model</label>
            <input type="text" class="form-input" name="model" placeholder="e.g., PC130-NI@-7" required>
          </div>

          <div class="form-group">
            <label class="form-label">Machine #</label>
            <input type="text" class="form-input" name="machineNumber" placeholder="e.g., N521950" required>
          </div>

          <div class="form-group">
            <label class="form-label">SPEC Pattern</label>
            <input type="text" class="form-input" name="specPattern" placeholder="e.g., NAC" required>
          </div>

          <div class="form-group">
            <label class="form-label">Order #</label>
            <input type="text" class="form-input" name="orderNumber" placeholder="e.g., S86NSS64D" required>
          </div>

          <div class="form-group">
            <label class="form-label">Cabin</label>
            <select class="form-select" name="cabin" required>
              <option value="">Select Cabin</option>
              <option value="AC">AC</option>
              <option value="NON-AC">NON-AC</option>
            </select>
          </div>

          <div class="form-group">
            <label class="form-label">Arm</label>
            <input type="text" class="form-input" name="arm" placeholder="e.g., 2.1M" required>
          </div>

          <div class="form-group">
            <label class="form-label">Bucket</label>
            <input type="text" class="form-input" name="bucket" placeholder="e.g., 0.64_CUM_NON-DGMS" required>
          </div>

          <div class="form-group">
            <label class="form-label">DGMS</label>
            <input type="text" class="form-input" name="dgms" placeholder="Optional">
          </div>

          <div class="form-group">
            <label class="form-label">Mesh Guard / Add</label>
            <input type="text" class="form-input" name="meshGuard" placeholder="e.g., Mesh Guard" required>
          </div>

          <div class="form-group">
            <label class="form-label">Production Start Date</label>
            <input type="text" class="form-input" name="productionStart" placeholder="e.g., 28/08/2019" required>
          </div>
        </div>

        <div class="modal-actions">
          <button type="button" class="btn-secondary" onclick="closeAddNewModal()">Cancel</button>
          <button type="submit" class="btn-primary">
            <span id="submitText">Add Production Plan</span>
            <span id="submitLoading" class="loading-dots" style="display: none;">
              <span></span><span></span><span></span>
            </span>
          </button>
        </div>
      </form>
    </div>
  </div>

  <script>
    // Complete data with all status types and revision numbers 1-5
    const data = [
      {
        id: 1,
        status: 'Kitting Completed',
        revisionNo: 1,
        market: 'DOMESTIC',
        prodMonth: 'Sep 19',
        model: 'PC130-NI@-7',
        machineNumber: 'N521950',
        specPattern: 'NAC',
        orderNumber: 'S86NSS64D',
        cabin: 'NON-AC',
        arm: '2.1M',
        bucket: '0.64_CUM_NON-DGMS',
        dgms: 'NON-DGMS',
        meshGuard: 'Mesh Guard',
        productionStart: '28/08/2019'
      },
      {
        id: 2,
        status: 'In Progress',
        revisionNo: 2,
        market: 'DOMESTIC',
        prodMonth: 'Sep 19',
        model: 'PC130-NI@-7',
        machineNumber: 'N521951',
        specPattern: 'NCA',
        orderNumber: 'S86NSS64D',
        cabin: 'NON-AC',
        arm: '2.1M TATT',
        bucket: '0.64_CUM_NON-DGMS',
        dgms: 'DGMS',
        meshGuard: 'Mesh Guard',
        productionStart: '29/08/2019'
      },
      {
        id: 3,
        status: 'Pending',
        revisionNo: 1,
        market: 'DOMESTIC',
        prodMonth: 'Sep 19',
        model: 'PC130-NI@-7',
        machineNumber: 'N521952',
        specPattern: 'NAC',
        orderNumber: 'S86NSS64D',
        cabin: 'NON-AC',
        arm: '2.1M TATT',
        bucket: '0.64_CUM_NON-DGMS',
        dgms: 'NON-DGMS',
        meshGuard: 'Mesh Guard',
        productionStart: '30/08/2019'
      },
      {
        id: 4,
        status: 'Quality Check',
        revisionNo: 3,
        market: 'DOMESTIC',
        prodMonth: 'Sep 19',
        model: 'PC130-NI@-7',
        machineNumber: 'N521953',
        specPattern: 'NAC',
        orderNumber: 'S86NSS64D',
        cabin: 'NON-AC',
        arm: '2.1M TATT',
        bucket: '0.64_CUM_NON-DGMS',
        dgms: 'DGMS',
        meshGuard: 'Mesh Guard',
        productionStart: '31/08/2019'
      },
      {
        id: 5,
        status: 'Kitting Completed',
        revisionNo: 4,
        market: 'DOMESTIC',
        prodMonth: 'Sep 19',
        model: 'PC130-NI@-7',
        machineNumber: 'N521954',
        specPattern: 'NAC',
        orderNumber: 'S86NSS64D',
        cabin: 'NON-AC',
        arm: '2.1M TATT',
        bucket: '0.64_CUM_NON-DGMS',
        dgms: '',
        meshGuard: 'Mesh Guard',
        productionStart: '01/09/2019'
      },
      {
        id: 6,
        status: 'On Hold',
        revisionNo: 2,
        market: 'DOMESTIC',
        prodMonth: 'Sep 19',
        model: 'PC130-NI@-7',
        machineNumber: 'N521955',
        specPattern: 'NAC',
        orderNumber: 'S86NSS64D',
        cabin: 'NON-AC',
        arm: '2.1M TATT',
        bucket: '0.64_CUM_NON-DGMS',
        dgms: '',
        meshGuard: 'Mesh Guard',
        productionStart: '02/09/2019'
      },
      {
        id: 7,
        status: 'In Progress',
        revisionNo: 5,
        market: 'DOMESTIC',
        prodMonth: 'Sep 19',
        model: 'PC130-NI@-7',
        machineNumber: 'N521956',
        specPattern: 'NAC',
        orderNumber: 'S86NSS64D',
        cabin: 'NON-AC',
        arm: '2.1M TATT',
        bucket: '0.64_CUM_NON-DGMS',
        dgms: '',
        meshGuard: 'Mesh Guard',
        productionStart: '03/09/2019'
      },
      {
        id: 8,
        status: 'Kitting Completed',
        revisionNo: 1,
        market: 'EXPORT',
        prodMonth: 'Oct 19',
        model: 'PC210-NI@-8MC',
        machineNumber: 'N721256',
        specPattern: 'NAA',
        orderNumber: 'S67ASS100',
        cabin: 'AC',
        arm: '2.8M TATT',
        bucket: '1.0_CUM_NON-DGMS',
        dgms: '',
        meshGuard: 'Mesh Guard',
        productionStart: '04/09/2019'
      },
      {
        id: 9,
        status: 'Quality Check',
        revisionNo: 3,
        market: 'EXPORT',
        prodMonth: 'Oct 19',
        model: 'PC210-NI@-10M0',
        machineNumber: 'N720117',
        specPattern: 'NCA',
        orderNumber: 'S86ASS900',
        cabin: 'AC',
        arm: '3.0M TATT',
        bucket: '1.0_CUM_NON-DGMS',
        dgms: '',
        meshGuard: 'Mesh Guard Ext Ls',
        productionStart: '05/09/2019'
      },
      {
        id: 10,
        status: 'Pending',
        revisionNo: 2,
        market: 'EXPORT',
        prodMonth: 'Oct 19',
        model: 'PC210-NI@-10M0',
        machineNumber: 'N720054',
        specPattern: 'NAA',
        orderNumber: 'S77ASS100',
        cabin: 'AC',
        arm: '2.8M TATT',
        bucket: '1.0_CUM_NON-DGMS',
        dgms: '',
        meshGuard: 'Mesh Guard',
        productionStart: '06/09/2019'
      },
      {
        id: 11,
        status: 'Cancelled',
        revisionNo: 1,
        market: 'DOMESTIC',
        prodMonth: 'Nov 19',
        model: 'PC300-8M0',
        machineNumber: 'N821789',
        specPattern: 'NCA',
        orderNumber: 'S89DEF456',
        cabin: 'AC',
        arm: '3.2M TATT',
        bucket: '1.5_CUM_XL',
        dgms: 'DGMS',
        meshGuard: 'Mesh Guard Ext',
        productionStart: '07/09/2019'
      },
      {
        id: 12,
        status: 'In Progress',
        revisionNo: 4,
        market: 'EXPORT',
        prodMonth: 'Nov 19',
        model: 'PC400-7',
        machineNumber: 'N921890',
        specPattern: 'NAA',
        orderNumber: 'E12XYZ789',
        cabin: 'NON-AC',
        arm: '3.5M',
        bucket: '2.0_CUM_HD',
        dgms: '',
        meshGuard: 'Mesh Guard',
        productionStart: '08/09/2019'
      },
      {
        id: 13,
        status: 'Kitting Completed',
        revisionNo: 2,
        market: 'DOMESTIC',
        prodMonth: 'Dec 19',
        model: 'PC130-NI@-7',
        machineNumber: 'N521957',
        specPattern: 'NAC',
        orderNumber: 'S90ABC123',
        cabin: 'AC',
        arm: '2.1M TATT',
        bucket: '0.64_CUM_NON-DGMS',
        dgms: '',
        meshGuard: 'Mesh Guard',
        productionStart: '09/09/2019'
      },
      {
        id: 14,
        status: 'On Hold',
        revisionNo: 3,
        market: 'EXPORT',
        prodMonth: 'Dec 19',
        model: 'PC210-NI@-10M0',
        machineNumber: 'N720118',
        specPattern: 'NCA',
        orderNumber: 'E34GHI456',
        cabin: 'AC',
        arm: '3.0M TATT',
        bucket: '1.0_CUM_NON-DGMS',
        dgms: 'DGMS',
        meshGuard: 'Mesh Guard Ext Ls',
        productionStart: '10/09/2019'
      },
      {
        id: 15,
        status: 'Quality Check',
        revisionNo: 5,
        market: 'DOMESTIC',
        prodMonth: 'Dec 19',
        model: 'PC300-8M0',
        machineNumber: 'N821790',
        specPattern: 'NAA',
        orderNumber: 'S91JKL789',
        cabin: 'NON-AC',
        arm: '3.2M',
        bucket: '1.5_CUM_XL',
        dgms: '',
        meshGuard: 'Mesh Guard',
        productionStart: '11/09/2019'
      },
      // Additional records for 5 pages (16-50)
      {
        id: 16,
        status: 'Kitting Completed',
        revisionNo: 1,
        market: 'DOMESTIC',
        prodMonth: 'Jan 20',
        model: 'PC200-8M0',
        machineNumber: 'N622001',
        specPattern: 'NAC',
        orderNumber: 'S92MNO123',
        cabin: 'AC',
        arm: '2.5M',
        bucket: '0.8_CUM_STD',
        dgms: '',
        meshGuard: 'Mesh Guard',
        productionStart: '12/09/2019'
      },
      {
        id: 17,
        status: 'In Progress',
        revisionNo: 2,
        market: 'EXPORT',
        prodMonth: 'Jan 20',
        model: 'PC350-8',
        machineNumber: 'N722002',
        specPattern: 'NCA',
        orderNumber: 'E45PQR456',
        cabin: 'NON-AC',
        arm: '3.0M TATT',
        bucket: '1.8_CUM_HD',
        dgms: 'DGMS',
        meshGuard: 'Mesh Guard Ext',
        productionStart: '13/09/2019'
      },
      {
        id: 18,
        status: 'Pending',
        revisionNo: 3,
        market: 'DOMESTIC',
        prodMonth: 'Feb 20',
        model: 'PC130-NI@-7',
        machineNumber: 'N521958',
        specPattern: 'NAA',
        orderNumber: 'S93STU789',
        cabin: 'AC',
        arm: '2.1M TATT',
        bucket: '0.64_CUM_NON-DGMS',
        dgms: '',
        meshGuard: 'Mesh Guard',
        productionStart: '14/09/2019'
      },
      {
        id: 19,
        status: 'Quality Check',
        revisionNo: 4,
        market: 'EXPORT',
        prodMonth: 'Feb 20',
        model: 'PC450-8',
        machineNumber: 'N822003',
        specPattern: 'NCA',
        orderNumber: 'E56VWX123',
        cabin: 'AC',
        arm: '3.8M',
        bucket: '2.5_CUM_XL',
        dgms: 'DGMS',
        meshGuard: 'Mesh Guard Ext Ls',
        productionStart: '15/09/2019'
      },
      {
        id: 20,
        status: 'On Hold',
        revisionNo: 5,
        market: 'DOMESTIC',
        prodMonth: 'Mar 20',
        model: 'PC210-NI@-10M0',
        machineNumber: 'N720119',
        specPattern: 'NAC',
        orderNumber: 'S94YZA456',
        cabin: 'NON-AC',
        arm: '3.0M TATT',
        bucket: '1.0_CUM_NON-DGMS',
        dgms: '',
        meshGuard: 'Mesh Guard',
        productionStart: '16/09/2019'
      }
    ];

    // Generate additional 30 records to reach 50 total (for 5 pages)
    for (let i = 21; i <= 50; i++) {
      const statuses = ['Kitting Completed', 'In Progress', 'Pending', 'Quality Check', 'On Hold', 'Cancelled'];
      const markets = ['DOMESTIC', 'EXPORT'];
      const models = ['PC130-NI@-7', 'PC210-NI@-8MC', 'PC210-NI@-10M0', 'PC300-8M0', 'PC400-7', 'PC450-8'];
      const cabins = ['AC', 'NON-AC'];
      const months = ['Apr 20', 'May 20', 'Jun 20', 'Jul 20', 'Aug 20', 'Sep 20'];

      data.push({
        id: i,
        status: statuses[Math.floor(Math.random() * statuses.length)],
        revisionNo: Math.floor(Math.random() * 5) + 1,
        market: markets[Math.floor(Math.random() * markets.length)],
        prodMonth: months[Math.floor(Math.random() * months.length)],
        model: models[Math.floor(Math.random() * models.length)],
        machineNumber: `N${Math.floor(Math.random() * 900000) + 100000}`,
        specPattern: ['NAC', 'NCA', 'NAA'][Math.floor(Math.random() * 3)],
        orderNumber: `${markets[Math.floor(Math.random() * 2)] === 'DOMESTIC' ? 'S' : 'E'}${Math.floor(Math.random() * 90) + 10}${String.fromCharCode(65 + Math.floor(Math.random() * 26))}${String.fromCharCode(65 + Math.floor(Math.random() * 26))}${String.fromCharCode(65 + Math.floor(Math.random() * 26))}${Math.floor(Math.random() * 900) + 100}`,
        cabin: cabins[Math.floor(Math.random() * cabins.length)],
        arm: ['2.1M', '2.5M', '2.8M', '3.0M', '3.2M', '3.5M', '3.8M'][Math.floor(Math.random() * 7)] + (Math.random() > 0.5 ? ' TATT' : ''),
        bucket: ['0.64_CUM_NON-DGMS', '0.8_CUM_STD', '1.0_CUM_NON-DGMS', '1.5_CUM_XL', '1.8_CUM_HD', '2.0_CUM_HD', '2.5_CUM_XL'][Math.floor(Math.random() * 7)],
        dgms: Math.random() > 0.5 ? 'DGMS' : 'NON-DGMS',
        meshGuard: ['Mesh Guard', 'Mesh Guard Ext', 'Mesh Guard Ext Ls'][Math.floor(Math.random() * 3)],
        productionStart: `${Math.floor(Math.random() * 28) + 1}/${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}/2019`
      });
    }

    function createStatusBadge(status) {
      const statusClass = status.toLowerCase().replace(/\s+/g, '-');
      return `<span class="status-badge status-${statusClass}">${status}</span>`;
    }

    function createActionIcons(row) {
      return `
        <div class="action-icons">
          <span class="material-icons action-icon" title="View Production Details">visibility</span>
          <span class="material-icons action-icon" title="Edit Production Plan">edit</span>
          <span class="material-icons action-icon" title="Copy Production Plan">content_copy</span>
          <span class="material-icons action-icon" title="Delete Production Plan">delete</span>
        </div>
      `;
    }

    let currentData = data;
    let currentPage = 1;
    const itemsPerPage = 10; // 10 items per page to get 5 pages with 50 total items

    function loadTable(dataToShow = currentData) {
      const tbody = document.getElementById('tableBody');
      const startIndex = (currentPage - 1) * itemsPerPage;
      const endIndex = startIndex + itemsPerPage;
      const pageData = dataToShow.slice(startIndex, endIndex);

      tbody.innerHTML = pageData.map(row => `
        <tr>
          <td>${createActionIcons(row)}</td>
          <td>${createStatusBadge(row.status)}</td>
          <td>${row.revisionNo}</td>
          <td>${row.market}</td>
          <td>${row.prodMonth}</td>
          <td>${row.model}</td>
          <td>${row.machineNumber}</td>
          <td>${row.specPattern}</td>
          <td>${row.orderNumber}</td>
          <td>${row.cabin}</td>
          <td>${row.arm}</td>
          <td>${row.bucket}</td>
          <td>${row.dgms}</td>
          <td>${row.meshGuard}</td>
          <td>${row.productionStart}</td>
        </tr>
      `).join('');

      updatePagination(dataToShow);
    }

    function updatePagination(dataToShow) {
      const totalPages = Math.ceil(dataToShow.length / itemsPerPage);
      const startItem = ((currentPage - 1) * itemsPerPage) + 1;
      const endItem = Math.min(currentPage * itemsPerPage, dataToShow.length);

      document.querySelector('.pagination-info').textContent =
        `Showing ${startItem}-${endItem} of ${dataToShow.length} entries`;

      const controls = document.querySelector('.pagination-controls');
      controls.innerHTML = '';

      for (let i = 1; i <= totalPages; i++) {
        const btn = document.createElement('button');
        btn.className = `page-btn ${i === currentPage ? 'active' : ''}`;
        btn.textContent = i;
        btn.onclick = () => {
          currentPage = i;
          loadTable(dataToShow);
        };
        controls.appendChild(btn);
      }
    }

    function filterByStatus(status) {
      // Update active button
      document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
      event.target.classList.add('active');

      if (status === 'All') {
        currentData = data;
      } else {
        currentData = data.filter(row => row.status.includes(status));
      }

      currentPage = 1;
      loadTable(currentData);
    }

    function filterByColumn(column, value) {
      if (!value) {
        currentData = data;
      } else {
        currentData = data.filter(row =>
          row[column] && row[column].toString().toLowerCase().includes(value.toLowerCase())
        );
      }
      currentPage = 1;
      loadTable(currentData);
    }

    function clearFilters() {
      // Reset all filters
      document.querySelectorAll('.filter-select').forEach(select => select.value = '');
      document.querySelector('.quick-search').value = '';

      // Reset status filter
      document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
      document.querySelector('.filter-btn').classList.add('active');

      currentData = data;
      currentPage = 1;
      loadTable(currentData);
    }

    function setupEventListeners() {
      // Status filter buttons
      document.querySelectorAll('.filter-btn').forEach((btn, index) => {
        const statuses = ['All', 'Completed', 'Progress', 'Pending'];
        btn.onclick = () => filterByStatus(statuses[index]);
      });

      // Global search
      document.querySelector('.quick-search').oninput = (e) => {
        const query = e.target.value.toLowerCase();
        if (!query) {
          currentData = data;
        } else {
          currentData = data.filter(row =>
            Object.values(row).some(value =>
              value.toString().toLowerCase().includes(query)
            )
          );
        }
        currentPage = 1;
        loadTable(currentData);
      };

      // Add New button
      document.querySelector('.add-new-btn').onclick = openAddNewModal;

      // Modern search inputs
      setupModernSearch();
    }

    function setupModernSearch() {
      const searchInputs = document.querySelectorAll('.modern-search-input');
      const dropdowns = document.querySelectorAll('.search-dropdown');

      searchInputs.forEach(input => {
        const column = input.getAttribute('data-column');
        const dropdown = document.querySelector(`.search-dropdown[data-column="${column}"]`);

        // Input focus - show dropdown if exists
        input.addEventListener('focus', () => {
          if (dropdown) {
            hideAllDropdowns();
            dropdown.classList.add('show');
          }
        });

        // Input typing - filter data
        input.addEventListener('input', (e) => {
          const query = e.target.value.toLowerCase();
          applyModernFilter(column, query);

          if (dropdown) {
            filterDropdownItems(dropdown, query);
          }
        });

        // Dropdown item selection
        if (dropdown) {
          dropdown.addEventListener('click', (e) => {
            if (e.target.classList.contains('dropdown-item')) {
              const value = e.target.getAttribute('data-value');
              input.value = value;
              applyModernFilter(column, value.toLowerCase());
              dropdown.classList.remove('show');
            }
          });
        }
      });

      // Close dropdowns when clicking outside
      document.addEventListener('click', (e) => {
        if (!e.target.closest('.search-input-container')) {
          hideAllDropdowns();
        }
      });
    }

    function hideAllDropdowns() {
      document.querySelectorAll('.search-dropdown').forEach(dropdown => {
        dropdown.classList.remove('show');
      });
    }

    function filterDropdownItems(dropdown, query) {
      const items = dropdown.querySelectorAll('.dropdown-item');
      items.forEach(item => {
        const text = item.textContent.toLowerCase();
        if (text.includes(query) || query === '') {
          item.style.display = 'block';
        } else {
          item.style.display = 'none';
        }
      });
    }

    function applyModernFilter(column, query) {
      // Get all current filter values
      const filters = {};
      document.querySelectorAll('.modern-search-input').forEach(input => {
        const col = input.getAttribute('data-column');
        const val = input.value.toLowerCase().trim();
        if (val) {
          filters[col] = val;
        }
      });

      // Apply all filters
      currentData = data.filter(row => {
        return Object.keys(filters).every(key => {
          const filterValue = filters[key];
          const rowValue = (row[key] || '').toString().toLowerCase();
          return rowValue.includes(filterValue);
        });
      });

      currentPage = 1;
      loadTable(currentData);
    }

    function clearAllFilters() {
      // Clear all modern search inputs
      document.querySelectorAll('.modern-search-input').forEach(input => {
        input.value = '';
      });

      // Clear global search
      document.querySelector('.quick-search').value = '';

      // Reset status filter
      document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
      document.querySelector('.filter-btn').classList.add('active');

      // Reset data
      currentData = data;
      currentPage = 1;
      loadTable(currentData);

      // Hide dropdowns
      hideAllDropdowns();
    }

      // Action icons
      document.addEventListener('click', (e) => {
        if (e.target.classList.contains('action-icon')) {
          const action = e.target.textContent;
          const row = e.target.closest('tr');
          const rowIndex = Array.from(row.parentElement.children).indexOf(row);
          const rowData = currentData[rowIndex];

          switch(action) {
            case 'visibility':
              alert(`View details for ${rowData.orderNumber}`);
              break;
            case 'edit':
              alert(`Edit ${rowData.orderNumber}`);
              break;
            case 'content_copy':
              alert(`Copy ${rowData.orderNumber}`);
              break;
            case 'delete':
              if (confirm(`Delete ${rowData.orderNumber}?`)) {
                alert(`${rowData.orderNumber} deleted`);
              }
              break;
          }
        }
      });
    }

    // Tree navigation function
    function toggleNavGroup(element) {
      const navGroup = element.parentElement;
      const isExpanded = navGroup.classList.contains('expanded');

      // Close all other nav groups
      document.querySelectorAll('.nav-group').forEach(group => {
        group.classList.remove('expanded');
      });

      // Toggle current group
      if (!isExpanded) {
        navGroup.classList.add('expanded');
      }
    }

    // Modal functions
    function openAddNewModal() {
      document.getElementById('addNewModal').classList.add('show');
      document.body.style.overflow = 'hidden';
    }

    function closeAddNewModal() {
      document.getElementById('addNewModal').classList.remove('show');
      document.body.style.overflow = 'auto';
      document.getElementById('addNewForm').reset();
    }

    function addNewProductionPlan(formData) {
      // Generate new ID
      const newId = Math.max(...data.map(item => item.id)) + 1;

      // Create new record
      const newRecord = {
        id: newId,
        status: formData.status,
        revisionNo: parseInt(formData.revisionNo),
        market: formData.market,
        prodMonth: formData.prodMonth,
        model: formData.model,
        machineNumber: formData.machineNumber,
        specPattern: formData.specPattern,
        orderNumber: formData.orderNumber,
        cabin: formData.cabin,
        arm: formData.arm,
        bucket: formData.bucket,
        dgms: formData.dgms || '',
        meshGuard: formData.meshGuard,
        productionStart: formData.productionStart
      };

      // Add to data
      data.push(newRecord);
      currentData = data;

      // Refresh table
      loadTable();

      // Close modal
      closeAddNewModal();

      // Show success message
      showToast('Production plan added successfully!', 'success');
    }

    function showToast(message, type = 'info') {
      // Create toast element
      const toast = document.createElement('div');
      toast.className = `toast toast-${type}`;
      toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#28a745' : '#17a2b8'};
        color: white;
        padding: 12px 20px;
        border-radius: 4px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10001;
        font-size: 14px;
        animation: slideInRight 0.3s ease;
      `;
      toast.textContent = message;

      document.body.appendChild(toast);

      // Remove after 3 seconds
      setTimeout(() => {
        toast.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
          if (toast.parentNode) {
            document.body.removeChild(toast);
          }
        }, 300);
      }, 3000);
    }

    // Initialize
    document.addEventListener('DOMContentLoaded', () => {
      // Expand Production Plan by default
      document.querySelector('.nav-group').classList.add('expanded');

      loadTable();
      setupEventListeners();

      // Add form submit handler
      document.getElementById('addNewForm').addEventListener('submit', (e) => {
        e.preventDefault();

        // Show loading
        document.getElementById('submitText').style.display = 'none';
        document.getElementById('submitLoading').style.display = 'inline-flex';

        // Get form data
        const formData = new FormData(e.target);
        const data = Object.fromEntries(formData.entries());

        // Simulate API call delay
        setTimeout(() => {
          addNewProductionPlan(data);

          // Reset button
          document.getElementById('submitText').style.display = 'inline';
          document.getElementById('submitLoading').style.display = 'none';
        }, 1000);
      });

      // Close modal on overlay click
      document.getElementById('addNewModal').addEventListener('click', (e) => {
        if (e.target === e.currentTarget) {
          closeAddNewModal();
        }
      });
    });
  </script>
</body>
</html>
